//using UnityEngine;
//using System.Collections;
//using System.Collections.Generic;
//using System;
//using Assets.Scripts.CasualTools.Dialogs;

//using UnityEngine.UI;
//using Assets.Scripts.CasualTools.Common.Pooling;
//using TMPro;
//using DG.Tweening;
//using Assets.Scripts.DataHelpers;
//using I2.Loc;
//using Utils.Analytics;
//using LGPOfflineSDK;

using Assets.Scripts.CasualTools.Common.Pooling;
using Assets.Scripts.CasualTools.Dialogs;
using Assets.Scripts.DataHelpers;
using DG.Tweening;
using I2.Loc;
using LGPOfflineSDK;
using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Utils.Analytics;

[System.Serializable]
public enum WatchVideoType
{
    None,
    MainNoLife, //主界面体力不足
    LifeDonate,  //体力赠送
    StageSelectCoin, //关卡选择界面
    FirstFreeRevive,  //首次免费复活
    SettlementCoin,    //结算界面金币
    SettlementProp,//结算界面道具
    ShopCoin,    //商店界面
    MainCoin,
    BuyProp, //购买道具
}

public enum WatchVideoSite
{
    None,
    LiveEmpty = -1,
    LivePage = -2,
    StageSelectPage = -3,
    ReviviePage = -4,
    WinPage = -5,
    LostPage = -6,
    ShopPage = -7,
    MainPate = -8,
    BuyPropPage = -9,
}

public class ADControl : MonoBehaviour
{
    public static ADControl instance;
    public const string IOSControlSaving = "ADControlSaving";

    //服务器是否有观看视频次数限制
    private bool BeServerHasWatchTime
    {
        get { return UPlayerUtils.GetInt("BeServerHasWatchTime", 1) == 1 ? true : false; }
        set { UPlayerUtils.Save("BeServerHasWatchTime", value ? 1 : 0); }
    }

    //视频观看控制
    [System.Serializable]
    public class WatchVideoConfig
    {
        public string watchVideo;
        public WatchVideoType watchType;
        public int MaxShowTimes;

        public List<ItemData> rewardItemDatas;

        [System.NonSerialized]
        public int saveShowTimes;
    }

    public List<WatchVideoConfig> watchVideoConfigs;

    private DateTime _nextDateTime;
    private bool checkNextDay;

    public double startLevelTimes;

    private float delayTime = 3;

    [System.NonSerialized]
    public bool BeShowVideo;

    private void Awake()
    {
        instance = this;
        checkNextDay = true;
        ReadData();
    }

    private void Update()
    {
        delayTime -= Time.deltaTime;
        if (checkNextDay)
        {
            CheckNewDay();
        }
    }

    private void CheckNewDay()
    {
        if (DateTime.Now.DayOfYear != _nextDateTime.DayOfYear)
        {
            ResetData();
        }
        checkNextDay = false;
    }

    #region 视频观看次数

    private void ResetDataByDaily()
    {
        ResetData();
        BeServerHasWatchTime = true;
    }

    private void ReadData()
    {
        string time = UPlayerUtils.GetString("ADControl_DataTime", "NULL");
        if (!time.Equals("NULL"))
        {
            _nextDateTime = DateTime.Parse(time);
        }
        for (int i = 0; i < watchVideoConfigs.Count; i++)
        {
            watchVideoConfigs[i].saveShowTimes = GetWatchVideoSaveTimes(watchVideoConfigs[i].watchType);
        }
    }

    private void ResetData()
    {
        _nextDateTime = DateTime.Now;
        UPlayerUtils.Save("ADControl_DataTime", _nextDateTime.ToString());
        ReSetWatchVideoTime();
    }

    private int GetWatchVideoSaveTimes(WatchVideoType type)
    {
        return UPlayerUtils.GetInt(string.Format("{0}_{1}_Time", IOSControlSaving, type.ToString()));
    }

    private void SetWatchVideoSaveTimes(WatchVideoType type, int times)
    {
        UPlayerUtils.Save(string.Format("{0}_{1}_Time", IOSControlSaving, type.ToString()), times);
    }

    #endregion 视频观看次数

    private WatchVideoConfig GetWatchVideoConfig(WatchVideoType type)
    {
        int count = watchVideoConfigs.Count;
        for (int i = 0; i < count; i++)
        {
            if (type == watchVideoConfigs[i].watchType)
            {
                return watchVideoConfigs[i];
            }
        }
        return null;
    }

    /// <summary>
    /// 获取特定位置视频观看次数
    /// </summary>
    /// <param name="type"></param>
    /// <param name="beMax"></param>
    /// <returns></returns>
    private int GetWatchVideoShowTime(WatchVideoType type, bool beMax)
    {
        WatchVideoConfig attr = GetWatchVideoConfig(type);
        return !beMax ? attr.saveShowTimes : attr.MaxShowTimes;
    }

    /// <summary>
    /// 当天播放次数已完
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public bool CheckWatchVideoShowTimeOver(WatchVideoType type)
    {
        if (!BeServerHasWatchTime) return false;
        WatchVideoConfig attr = GetWatchVideoConfig(type);
        return attr.saveShowTimes >= attr.MaxShowTimes && attr.MaxShowTimes != -1;
    }

    private List<ItemData> GetWatchVideoReward(WatchVideoType type)
    {
        WatchVideoConfig attr = GetWatchVideoConfig(type);
        return attr.rewardItemDatas;
    }

    private void AddWatchVideoReward(WatchVideoType type)
    {
        List<ItemData> rewardList = GetWatchVideoReward(type);
        int count = rewardList.Count;
        for (int i = 0; i < count; i++)
        {
            if (rewardList[i].itemId == SDKItemID.Coins)
            {
                InventoryHelper.Instance.AddItemAmount(InventoryItemType.Coins, rewardList[i].count, true);
            }
            //else if (rewardList[i].itemId == SDKItemID.Life)
            //{
            //    InventoryHelper.Instance.AddItemAmount(InventoryItemType.Coins, rewardList[i].count, true);
            //}
            //string tip = USDKDataSupport.Instance.GetItemName(rewardList[i].id);
            //USDKDataSupport.Instance.AddItemCount(rewardList[i].id, rewardList[i].count);
        }
    }

    #region xiaoming

    //xiaoming 播放广告

    private void Start()
    {
        this.ReminderTextPrefab.CreatePool(2);
    }

    private void OnDestroy()
    {
        this.ReminderTextPrefab.DestroyAllPooled();
    }

    private void ReSetWatchVideoTime()
    {
        for (int i = 0; i < watchVideoConfigs.Count; i++)
        {
            watchVideoConfigs[i].saveShowTimes = 0;
            SetWatchVideoSaveTimes(watchVideoConfigs[i].watchType, 0);
        }
    }


    public int GetWatchVideoRemanentTime(WatchVideoType type)
    {
        WatchVideoConfig attr = GetWatchVideoConfig(type);
        return System.Math.Max(attr.MaxShowTimes - attr.saveShowTimes, 0);
    }

    public void ShowWatchVideo(WatchVideoType type, WatchVideoSite site, string reminder, int count, Action<bool> successBack = null)
    {
        string content = string.Empty;
        if (reminder != string.Empty)
        {
            if (count != 0)
            {
                content = ScriptLocalization.Get("Get") + ScriptLocalization.Get(reminder) + "+" + count;
            }
            else
            {
                content = ScriptLocalization.Get("Get") + ScriptLocalization.Get(reminder);
            }
            if (reminder == "Coins2")
            {
                content = string.Empty;
            }
        }
        SetControlWatchVideo(type, site, content, successBack);
    }

    public GameObject ReminderTextPrefab;

    public void ShowReminder(string content)
    {
        Vector3 startPosition = Vector3.down * 2.45f;
        TextMeshPro amountText = this.ReminderTextPrefab.Spawn().GetComponent<TextMeshPro>();
        if (amountText != null)
        {
            Sequence sequence = DOTween.Sequence().SetEase(Ease.Linear);
            sequence.SetRecyclable(false);
            amountText.enabled = true;
            amountText.color = new Color(1f, 1f, 1f, 0f);
            amountText.transform.position = startPosition;
            amountText.transform.localScale = Vector3.zero;
            amountText.text = content;
            Tweener tweener = amountText.transform.DOMoveY(startPosition.y + 2f, 2f, false).SetEase(Ease.InQuad);
            tweener.easeOvershootOrAmplitude = 2f;
            sequence.Insert(0f, amountText.DOFade(1f, 1f));
            sequence.Insert(0f, amountText.transform.DOScale(Vector3.one, 0.5f).SetEase(Ease.OutSine));
            sequence.Insert(0f, tweener);
            sequence.Insert(1.5f, amountText.DOFade(0f, 0.5f));
            sequence.OnComplete(delegate
            {
                if (amountText == null)
                {
                    return;
                }
                amountText.enabled = false;
                amountText.Recycle<TextMeshPro>();
            });
        }
    }

    #endregion xiaoming

    private void SetControlWatchVideo(WatchVideoType type, WatchVideoSite site, string reminder, Action<bool> successBack = null, bool rewardPanel = false)
    {
        WatchVideoConfig attr = GetWatchVideoConfig(type);
        if (attr == null)
        {
            //UPanelManager.Instance.ShowTips("暂未配置视频");
            return;
        }

        if (attr.saveShowTimes >= attr.MaxShowTimes && attr.MaxShowTimes != -1)
        {
            string content = ScriptLocalization.Get("VideoWatchTimeEnough");
            ShowReminder(content);
            //UPanelManager.Instance.ShowTips(/*Localization.Get*/("VideoWatchTimeEnough"));
        }
        else
        {
            ADControl.instance.SetWatchVideo((int)site, reminder, (result) =>
             {
                 if (result)
                 {
                     List<ItemData> rewardList = GetWatchVideoReward(type);
                     AddWatchVideoReward(type);
                     if (rewardPanel)
                     {
                     }
                     attr.saveShowTimes++;
                     SetWatchVideoSaveTimes(attr.watchType, attr.saveShowTimes);
                 }
                 if (successBack != null) successBack(result);
             });
        }
    }

    #region 视频广告

    private static bool HasMusicOn;

    /// <summary>
    /// 播放广告    AD_Log_Sit_{0} 广告位置
    ///  1：开始播放    2：播放成功   3：跳过    4：上限    5：失败
    /// </summary>
    /// <param name="showSite"></param>
    /// <param name="callBack"></param>
    bool beShowBanner;
    private void SetWatchVideo(int showSite, string reminder, Action<bool> callBack)
    {
        //LGPOfflineSDKAPI.SendStreamEvent("AD_Log_Sit_" + showSite, "1", false);//开始观看
        BeShowVideo = true;
        SpriteButtonListener.Instance.EnableClicks = false;
        beShowBanner = LGPOfflineSDKAPI.HasBannerShow();
        //GMGSDK.HideBanner();
        if (Application.isEditor)
        {
            SetMusicInit();
#if UNITY_IOS
            if(IOSPaymentManager.instance.payLoadingUI) IOSPaymentManager.instance.payLoadingUI.SetActive(true);
#endif
            //App.PayWait.SetActive(true);
            StartCoroutine(DelayCallBackTest((result) =>
            {
                //App.PayWait.SetActive(false);
#if UNITY_IOS
                if (IOSPaymentManager.instance.payLoadingUI) IOSPaymentManager.instance.payLoadingUI.SetActive(false);
#endif
                SetMusicReset();
                if (reminder != string.Empty)
                {
                    string content = ScriptLocalization.Get("VideoWatchSuccess") + " " + reminder;
                    ShowReminder(content);
                }
                //UPanelManager.Instance.ShowTips(/*Localization.Get*/("VideoWatchSuccess"));
                BeShowVideo = false;
                if (callBack != null) callBack(result);

                SpriteButtonListener.Instance.EnableClicks = true;
            }));
        }
        else
        {
            Debug.LogError("视频开始");
            SetMusicInit();
            LGPOfflineSDKAPI.ShowAdAndVideo(showSite, (result, adType, resultCode, cdTimeRemain) =>
            {
                string content = string.Empty;
                Debug.LogError("视频结束");
                SpriteButtonListener.Instance.EnableClicks = true;
                SetMusicReset();
                if (adType == LGPOfflineSDK.PushType.AD) return;
                BeShowVideo = false;
                if (resultCode == LGPOfflineSDK.AdPlayResultCode.上限)
                {
                    if (BeServerHasWatchTime)
                    {
                        BeServerHasWatchTime = false;
                    }
                }
                else
                {
                    BeServerHasWatchTime = true;
                }

                switch (resultCode)
                {
                    case LGPOfflineSDK.AdPlayResultCode.CD:

                        break;

                    case LGPOfflineSDK.AdPlayResultCode.上限:
                        content = ScriptLocalization.Get("VideoWatchTimeEnough");
                        ShowReminder(content);
                        //UPanelManager.Instance.ShowTips(/*Localization.Get*/("VideoWatchTimeEnough"));
                        BeServerHasWatchTime = false;
                        break;

                    case LGPOfflineSDK.AdPlayResultCode.VIP跳过:
                        break;

                    case LGPOfflineSDK.AdPlayResultCode.成功:
                        if (callBack != null)
                            callBack(true);
                        if (content != string.Empty)
                        {
                            content = ScriptLocalization.Get("VideoWatchSuccess");
                            ShowReminder(content);
                        }
                        //ShowReminder("VideoWatchSuccess");
                        //GameTipsPanel.Show(GameTipType.提示字, "视频播放成功！",false);
                        //UPanelManager.Instance.ShowTips(Localization.Get("VideoWatchSuccess"));

                        return;

                    case LGPOfflineSDK.AdPlayResultCode.跳过:
                        content = ScriptLocalization.Get("VideoWatchSkip");
                        ShowReminder(content);
                        //ShowReminder("VideoWatchSkip");
                        //UPanelManager.Instance.ShowTips(/*Localization.Get*/("VideoWatchSkip"));
                        break;

                    default:
                        content = ScriptLocalization.Get("VideoWatchFail");
                        ShowReminder(content);
                        //ShowReminder("VideoWatchFail");
                        //UPanelManager.Instance.ShowTips(/*Localization.Get*/("VideoWatchFail"));
                        break;
                }
                if (callBack != null) callBack(false);
            });
        }

        Debug.Log("showDomobVideo");
    }

    private void SetMusicInit()
    {
        HasMusicOn = UserSettings.Instance.IsMusicEnabled();
        if (HasMusicOn)
        {
            //待定
            USoundManager.PauseAll();
            UserSettings.Instance.SetMusicEnabled(false);
            EventSender.SendEventThreaded("MusicStatusChanged", new int?(LevelHelper.Instance.GetHighestLevelUserWon() + 1), null, UserIdHelper.Instance.UserId.ToString(), true ? "0" : "1", null);
            Debug.LogError("视频关闭音效");
        }
    }

    private void SetMusicReset()
    {
        if (HasMusicOn)
        {
            //待定
            USoundManager.ResumeAll();
            UserSettings.Instance.SetMusicEnabled(true);
            EventSender.SendEventThreaded("MusicStatusChanged", new int?(LevelHelper.Instance.GetHighestLevelUserWon() + 1), null, UserIdHelper.Instance.UserId.ToString(), true ? "0" : "1", null);
            Debug.LogError("视频打开音效");
        }
    }

    private IEnumerator DelayCallBackTest(Action<bool> callBack)
    {
        yield return new WaitForSecondsRealtime(1f);
        if (callBack != null)
        {
            callBack(true);
        }
    }

    #endregion 视频广告

    #region 原生广告

    /// <summary>
    /// 获取原生广告内容
    /// 获取原生广告推送的数据(选接，需与运营确认)
    /// GMGSDK.GetGamePromotionData(是否只获取一条数据 , 获取数据的位置，获取到数据后的回调);
    /// 发送原生广告展示日志(在原生广告被展示出来的时候发送) GMGSDK.SendGamePromotionLog(展示位置, 当前广告的ID);
    ///
    ///
    /// GameShowData
    ///     string title  展示的标题
    ///     string icon logo图片下载地址
    ///     string img 大图下载地址
    ///     string banner Banner下载地址
    ///     Action<int> clickEvt 点击事件处理，游戏点击的时候调用
    ///
    /// </summary>
    //private void GetGamePromotionData(int posId, Action<GameShowData, Texture> showAction)
    //{
    //    GMGSDK.GetGamePromotionData(true, posId, (_onGotDatas) =>
    //    {
    //        if (_onGotDatas == null || _onGotDatas.Count <= 0)
    //        {
    //            showAction(null, null);
    //        }
    //        else
    //        {
    //            //string banner = _onGotDatas[0].banner;
    //            //Action<int> clickEvt = _onGotDatas[0].clickEvt;
    //            //string description = _onGotDatas[0].description;
    //            //string downloadUrl = _onGotDatas[0].downloadUrl;
    //            //string icon = _onGotDatas[0].icon;
    //            //int id = _onGotDatas[0].id;
    //            //string img = _onGotDatas[0].img;
    //            //bool isHorizontal = _onGotDatas[0].isHorizontal;
    //            //string packageName = _onGotDatas[0].packageName;
    //            //string title = _onGotDatas[0].title;

    //            StartCoroutine(DownIcon(_onGotDatas[0], posId, showAction));
    //        }
    //    });
    //}

    /// <summary>
    /// 展示推广
    /// </summary>
    /// <param name="_data"></param>
    /// <param name="id">广告位置</param>
    //private void ClickGamePromotionAD(GameShowData _data, int id)
    //{
    //    _data.clickEvt.Invoke(id);
    //}

    //private IEnumerator DownIcon(GameShowData _data, int posId, Action<GameShowData, Texture> action)
    //{
    //    yield return StartCoroutine(DownGamePromotionIcon(_data, posId, action));
    //}

    //private IEnumerator DownGamePromotionIcon(GameShowData _data, int posId, Action<GameShowData, Texture> action)
    //{
    //    Debug.LogError("下载icon地址" + _data.icon);
    //    WWW myWWW = new WWW(_data.icon);
    //    if (Time.timeScale == 0)
    //    {
    //        while (myWWW.texture != null)
    //        {
    //            yield return null;
    //        }
    //    }
    //    else
    //        yield return myWWW;

    //    if (myWWW.texture != null)
    //    {
    //        Debug.LogError("下载icon成功，展示icon");
    //    }
    //    action(_data, myWWW.texture);
    //    GMGSDK.SendGamePromotionLog(posId, _data.id);
    //}

    #endregion 原生广告

    #region 插屏广告

    public void ShowAD(int site, Action<bool> showBack = null)
    {
        if (UPlayerUtils.GetInt("DiscountBundleBuyed", 0) == 1)
        {
            return;
        }

        if (Application.isEditor && site > 0)
        {
            UPanelManager.Instance.ShowTips("插屏广告");
        }
        LGPOfflineSDKAPI.ShowAdAndVideo(site, (result, adType, resultCode, cdTimeRemain) =>
        {
            //if (adType != PushType.AD) return;
            switch (resultCode)
            {
                case LGPOfflineSDK.AdPlayResultCode.成功:
                    if (showBack != null)
                    {
                        showBack(true);
                    }
                    return;
            }

            if (showBack != null)
            {
                showBack(false);
            }
        });
    }

    #endregion 插屏广告
}