﻿// dnSpy decompiler from Assembly-CSharp.dll class: Assets.Scripts.Dialogs.SimpleDialog
using Assets.Scripts.CasualTools.Dialogs;
using Assets.Scripts.Utils;
using DG.Tweening;
using System;
using UnityEngine;

namespace Assets.Scripts.Dialogs
{
    public abstract class SimpleDialog : Dialog
    {
        protected void EnableColliders()
        {
            if (this.Colliders == null)
            {
                return;
            }
            for (int i = 0; i < this.Colliders.Length; i++)
            {
                BoxCollider2D boxCollider2D = this.Colliders[i];
                if (boxCollider2D != null)
                {
                    boxCollider2D.enabled = true;
                }
            }
        }

        protected void DisableColliders()
        {
            if (this.Colliders == null)
            {
                return;
            }
            for (int i = 0; i < this.Colliders.Length; i++)
            {
                BoxCollider2D boxCollider2D = this.Colliders[i];
                if (boxCollider2D != null)
                {
                    boxCollider2D.enabled = false;
                }
            }
        }

        protected virtual void PlayedIn()
        {
        }

        public float EaseOutElastic(float time, float duration, float overshootOrAmplitude, float period)
        {
            float num = time / duration;
            return (Math.Abs(num) >= 1E-06f) ? ((Math.Abs(num - 1f) >= 1E-06f) ? (Mathf.Pow(2f, -10f * num) * Mathf.Sin((num * 10f - 0.75f) * 2.09439516f) + 1f) : 1f) : 0f;
        }

        public override void PlayDialogInAnimation()
        {
            this.ActivateDialog(true);
            this.PutMiddle();
            float x = base.gameObject.transform.localScale.x;
            this._inAnimation = DOTween.Sequence();
            this._inAnimation.Append(base.gameObject.transform.DOScale(x + 0.02f, 0.08f * this.t));
            this._inAnimation.Append(base.gameObject.transform.DOScale(x - 0.01f, 0.06f * this.t));
            this._inAnimation.Append(base.gameObject.transform.DOScale(x, 0.04f * this.t));
            this._inAnimation.AppendCallback(new TweenCallback(this.EnableColliders));
            this._inAnimation.AppendCallback(new TweenCallback(this.PlayedIn));
            this._inAnimation.OnComplete(delegate
            {
                this._inAnimation = null;
            });
        }

        public override Sequence PlayDialogOutAnimation()
        {
            if (this._inAnimation != null && this._inAnimation.IsPlaying())
            {
                this._inAnimation.Kill(false);
            }
            this.DisableColliders();
            this.ActivateDialog(false);
            return null;
        }

        public override void PlayDialogOutFast()
        {
            if (this._inAnimation != null && this._inAnimation.IsPlaying())
            {
                this._inAnimation.Kill(false);
            }
            this.DisableColliders();
            this.ActivateDialog(false);
        }

        public override void DialogCreated()
        {
            base.FrameCreated = Time.frameCount;
            this.DisableColliders();
            this.PutMiddle();
        }

        public override void SetDialogBounds()
        {
            if (this.TouchBounds == null)
            {
                return;
            }
            Vector3 center = new Vector3
            {
                x = base.transform.position.x + this.TouchBounds.Offset.x,
                y = base.transform.position.y + this.TouchBounds.Offset.y,
                z = 0f
            };
            Vector3 size = new Vector3
            {
                x = this.TouchBounds.Size.x * base.transform.localScale.x,
                y = this.TouchBounds.Size.y * base.transform.localScale.y,
                z = 0f
            };
            this.DialogBounds = new Bounds(center, size);
        }

        public override Bounds GetDialogBounds()
        {
            return this.DialogBounds;
        }

        public void ActivateDialog(bool value)
        {
            base.gameObject.SetActive(value);
        }

        private void PutMiddle()
        {
            float y = (!CameraHelper.IsIpad) ? this.OffsetY : this.IPadOffsetY;
            base.gameObject.transform.position = new Vector3(0f, y, -2f);
        }

        private const float C4 = 2.09439516f;

        private float t = 1.5f;

        private Sequence _inAnimation;

        public BoxCollider2D[] Colliders;
    }
}