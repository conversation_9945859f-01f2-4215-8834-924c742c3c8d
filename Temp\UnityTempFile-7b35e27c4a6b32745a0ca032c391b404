/target:library
/out:Temp/Unity.PlasticSCM.Editor.dll
/nowarn:0169
/nowarn:0649
/debug:portable
/optimize-
/nostdlib+
/preferreduilang:en-US
/langversion:latest
/reference:Library/ScriptAssemblies/UnityEditor.UI.dll
/reference:Library/ScriptAssemblies/UnityEngine.UI.dll
/reference:Library/ScriptAssemblies/UnityEditor.TestRunner.dll
/reference:Library/ScriptAssemblies/UnityEngine.TestRunner.dll
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UNETModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEditor.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/UnityExtensions/Unity/UnityVR/Editor/UnityEditor.VR.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/Managed/UnityEditor.Graphs.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
/reference:E:/project/DogBlast_new/Library/PackageCache/com.unity.collab-proxy@1.14.18/Lib/Editor/PlasticSCM/unityplastic.dll
/reference:E:/project/DogBlast_new/Library/PackageCache/com.unity.collab-proxy@1.14.18/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll
/reference:E:/project/DogBlast_new/Library/PackageCache/com.unity.ext.nunit@1.0.6/net35/unity-custom/nunit.framework.dll
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/mscorlib.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Core.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Runtime.Serialization.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Xml.Linq.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Numerics.Vectors.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Net.Http.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.IO.Compression.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Microsoft.CSharp.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/System.Data.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/Microsoft.Win32.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/netstandard.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.AppContext.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Concurrent.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.NonGeneric.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Collections.Specialized.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Annotations.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.EventBasedAsync.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ComponentModel.TypeConverter.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Console.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Data.Common.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Contracts.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Debug.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.FileVersionInfo.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Process.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.StackTrace.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.Tools.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Diagnostics.TraceSource.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Drawing.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Dynamic.Runtime.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Calendars.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Globalization.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Compression.ZipFile.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.DriveInfo.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.FileSystem.Watcher.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.IsolatedStorage.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.MemoryMappedFiles.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.Pipes.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.IO.UnmanagedMemoryStream.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Expressions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Parallel.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Linq.Queryable.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Http.Rtc.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NameResolution.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.NetworkInformation.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Ping.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Requests.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Security.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.Sockets.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebHeaderCollection.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.Client.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Net.WebSockets.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ObjectModel.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.ILGeneration.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Emit.Lightweight.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Reflection.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Reader.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.ResourceManager.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Resources.Writer.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Handles.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Numerics.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Formatters.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Json.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Runtime.Serialization.Xml.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Claims.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Algorithms.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Csp.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Encoding.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Cryptography.X509Certificates.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.Principal.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Security.SecureString.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Duplex.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Http.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.NetTcp.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Primitives.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ServiceModel.Security.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.Encoding.Extensions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Text.RegularExpressions.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Overlapped.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Tasks.Parallel.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Thread.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.ThreadPool.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Threading.Timer.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.ValueTuple.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.ReaderWriter.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XDocument.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlDocument.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XmlSerializer.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/4.7.1-api/Facades/System.Xml.XPath.XDocument.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/UnityScript.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/UnityScript.Lang.dll"
/reference:"E:/ide/Unity/Unity 2019.4.40f1/Editor/Data/MonoBleedingEdge/lib/mono/unityscript/Boo.Lang.dll"
/define:UNITY_2019_4_40
/define:UNITY_2019_4
/define:UNITY_2019
/define:UNITY_5_3_OR_NEWER
/define:UNITY_5_4_OR_NEWER
/define:UNITY_5_5_OR_NEWER
/define:UNITY_5_6_OR_NEWER
/define:UNITY_2017_1_OR_NEWER
/define:UNITY_2017_2_OR_NEWER
/define:UNITY_2017_3_OR_NEWER
/define:UNITY_2017_4_OR_NEWER
/define:UNITY_2018_1_OR_NEWER
/define:UNITY_2018_2_OR_NEWER
/define:UNITY_2018_3_OR_NEWER
/define:UNITY_2018_4_OR_NEWER
/define:UNITY_2019_1_OR_NEWER
/define:UNITY_2019_2_OR_NEWER
/define:UNITY_2019_3_OR_NEWER
/define:UNITY_2019_4_OR_NEWER
/define:PLATFORM_ARCH_64
/define:UNITY_64
/define:UNITY_INCLUDE_TESTS
/define:ENABLE_AR
/define:ENABLE_AUDIO
/define:ENABLE_CACHING
/define:ENABLE_CLOTH
/define:ENABLE_EVENT_QUEUE
/define:ENABLE_MICROPHONE
/define:ENABLE_MULTIPLE_DISPLAYS
/define:ENABLE_PHYSICS
/define:ENABLE_TEXTURE_STREAMING
/define:ENABLE_UNET
/define:ENABLE_LZMA
/define:ENABLE_UNITYEVENTS
/define:ENABLE_VR
/define:ENABLE_WEBCAM
/define:ENABLE_UNITYWEBREQUEST
/define:ENABLE_WWW
/define:ENABLE_CLOUD_SERVICES
/define:ENABLE_CLOUD_SERVICES_COLLAB
/define:ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS
/define:ENABLE_CLOUD_SERVICES_ADS
/define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
/define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
/define:ENABLE_CLOUD_SERVICES_PURCHASING
/define:ENABLE_CLOUD_SERVICES_ANALYTICS
/define:ENABLE_CLOUD_SERVICES_UNET
/define:ENABLE_CLOUD_SERVICES_BUILD
/define:ENABLE_CLOUD_LICENSE
/define:ENABLE_EDITOR_HUB_LICENSE
/define:ENABLE_WEBSOCKET_CLIENT
/define:ENABLE_DIRECTOR_AUDIO
/define:ENABLE_DIRECTOR_TEXTURE
/define:ENABLE_MANAGED_JOBS
/define:ENABLE_MANAGED_TRANSFORM_JOBS
/define:ENABLE_MANAGED_ANIMATION_JOBS
/define:ENABLE_MANAGED_AUDIO_JOBS
/define:INCLUDE_DYNAMIC_GI
/define:ENABLE_MONO_BDWGC
/define:ENABLE_SCRIPTING_GC_WBARRIERS
/define:PLATFORM_SUPPORTS_MONO
/define:RENDER_SOFTWARE_CURSOR
/define:ENABLE_VIDEO
/define:PLATFORM_STANDALONE
/define:PLATFORM_STANDALONE_WIN
/define:UNITY_STANDALONE_WIN
/define:UNITY_STANDALONE
/define:ENABLE_RUNTIME_GI
/define:ENABLE_MOVIES
/define:ENABLE_NETWORK
/define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
/define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
/define:ENABLE_CLUSTER_SYNC
/define:ENABLE_CLUSTERINPUT
/define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
/define:ENABLE_WEBSOCKET_HOST
/define:ENABLE_MONO
/define:NET_4_6
/define:ENABLE_PROFILER
/define:DEBUG
/define:TRACE
/define:UNITY_ASSERTIONS
/define:UNITY_EDITOR
/define:UNITY_EDITOR_64
/define:UNITY_EDITOR_WIN
/define:ENABLE_UNITY_COLLECTIONS_CHECKS
/define:ENABLE_BURST_AOT
/define:UNITY_TEAM_LICENSE
/define:UNITY_PRO_LICENSE
/define:ENABLE_CUSTOM_RENDER_TEXTURE
/define:ENABLE_DIRECTOR
/define:ENABLE_LOCALIZATION
/define:ENABLE_SPRITES
/define:ENABLE_TERRAIN
/define:ENABLE_TILEMAP
/define:ENABLE_TIMELINE
/define:ENABLE_LEGACY_INPUT_MANAGER
/define:CSHARP_7_OR_LATER
/define:CSHARP_7_3_OR_NEWER
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssemblyInfo.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetMenu\AssetFilesFilterPatternsMenuBuilder.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetMenu\AssetMenuItems.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetMenu\AssetMenuOperations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetMenu\AssetMenuRoutingOperations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetMenu\AssetOperations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetMenu\AssetsSelection.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetMenu\Dialogs\CheckinDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetMenu\Dialogs\CheckinDialogOperations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetMenu\ProjectViewAssetSelection.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetOverlays\AssetStatus.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetOverlays\Cache\AssetStatusCache.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetOverlays\Cache\BuildPathDictionary.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetOverlays\Cache\LocalStatusCache.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetOverlays\Cache\LockStatusCache.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetOverlays\Cache\RemoteStatusCache.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetOverlays\Cache\SearchLocks.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetOverlays\DrawAssetOverlay.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetsUtils\AssetsPath.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetsUtils\GetSelectedPaths.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetsUtils\LoadAsset.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetsUtils\Processor\AssetModificationProcessor.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetsUtils\Processor\AssetPostprocessor.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetsUtils\Processor\AssetsProcessor.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetsUtils\Processor\PlasticAssetsProcessor.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetsUtils\ProjectPath.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetsUtils\RefreshAsset.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AssetsUtils\SaveAssets.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\AutoRefresh.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\CheckWorkspaceTreeNodeStatus.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\CloudProjectDownloader\CloudProjectDownloader.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\CloudProjectDownloader\CommandLineArguments.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\CloudProjectDownloader\DownloadRepositoryOperation.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\CloudProjectDownloader\ParseArguments.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\CollabMigration\MigrateCollabProject.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\CollabMigration\MigrationDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\CollabMigration\MigrationProgressRender.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\CollabPlugin.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\AutoConfig.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\ChannelCertificateUiImpl.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\AutoLogin.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\CloudEditionWelcomeWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\CreateOrganizationPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\CreatedOrganizationPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\OrganizationPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\SSOSignUpPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\SignInPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\SignInWithEmailPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CloudEdition\Welcome\WaitingSignInPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\ConfigurePartialWorkspace.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CredentialsDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\CredentialsUIImpl.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\EncryptionConfigurationDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\MissingEncryptionPasswordPromptHandler.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\SSOCredentialsDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\TeamEdition\TeamEditionConfigurationWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\ToolConfig.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\TurnOffPlastic\TurnOffPlasticWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Configuration\WriteLogConfiguration.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Developer\CheckinProgress.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Developer\GenericProgress.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Developer\IncomingChangesNotifier.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Developer\ProgressOperationHandler.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Developer\UpdateProgress.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportLineListViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportListHeaderState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Developer\UpdateReport\UpdateReportListView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\DrawGuiModeSwitcher.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\EnumExtensions.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\FindWorkspace.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\GetRelativePath.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Gluon\CheckinProgress.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Gluon\IncomingChangesNotifier.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Gluon\ProgressOperationHandler.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Gluon\UpdateProgress.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Gluon\UpdateReport\ErrorListViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportListHeaderState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Gluon\UpdateReport\UpdateReportListView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Help\BuildFormattedHelp.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Help\DrawHelpPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Help\HelpData.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Help\HelpFormat.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Help\HelpLink.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Help\HelpLinkData.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Help\HelpPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Help\TestingHelpData.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Inspector\DrawInspectorOperations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Inspector\InspectorAssetSelection.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\MetaPath.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\NewIncomingChanges.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\ParentWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\PlasticApp.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\PlasticMenuItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\PlasticNotification.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\PlasticPlugin.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\PlasticProjectSettingsProvider.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\PlasticWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\PlasticWindow\PlasticSCMWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\ProjectWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\SceneView\DrawSceneOperations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\SetupCloudProjectId.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\SwitchModeConfirmationDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Tool\BringWindowToFront.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Tool\FindTool.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Tool\IsExeAvailable.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Tool\LaunchInstaller.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Tool\LaunchTool.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Tool\ToolConstants.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Avatar\ApplyCircleMask.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Avatar\AvatarImages.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Avatar\GetAvatar.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\BoolSetting.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\CooldownWindowDelayer.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\DockEditorWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\DrawActionButton.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\DrawActionHelpBox.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\DrawActionToolbar.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\DrawSearchField.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\DrawSplitter.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\DrawTextBlockWithEndLink.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\DrawUserIcon.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\DropDownTextField.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\EditorDispatcher.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\EditorProgressBar.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\EditorProgressControls.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\EditorVersion.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\EditorWindowFocus.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\EnumPopupSetting.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\FindEditorWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\GUIActionRunner.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\GUISpace.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\GetPlasticShortcut.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\GuiEnabled.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\HandleMenuItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Images.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\MeasureMaxWidth.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Message\DrawDialogIcon.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Message\PlasticQuestionAlert.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\OverlayRect.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\PlasticDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\PlasticSplitterGUILayout.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Progress\DrawProgressForDialogs.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Progress\DrawProgressForMigration.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Progress\DrawProgressForOperations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Progress\DrawProgressForViews.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Progress\OperationProgressData.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Progress\ProgressControlsForDialogs.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Progress\ProgressControlsForMigration.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Progress\ProgressControlsForViews.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\ResponseType.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\RunModal.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\ShowWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\SortOrderComparer.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\StatusBar.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\TabButton.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Tree\DrawTreeViewEmptyState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Tree\DrawTreeViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Tree\GetChangesOverlayIcon.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Tree\ListViewItemIds.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Tree\TableViewOperations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Tree\TreeHeaderColumns.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Tree\TreeHeaderSettings.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\Tree\TreeViewItemIds.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UIElements\LoadingSpinner.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UIElements\ProgressControlsForDialogs.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UIElements\TabView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UIElements\UIElementsExtensions.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UnityConstants.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UnityEvents.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UnityMenuItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UnityPlasticGuiMessage.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UnityPlasticTimer.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UnityStyles.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UI\UnityThreadWaiter.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\UnityConfigurationChecker.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\VCSPlugin.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\ViewSwitcher.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Branch\BranchListViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Branch\BranchesListHeaderState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Branch\BranchesListView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Branch\BranchesSelection.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Branch\BranchesTab.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Branch\BranchesViewMenu.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Branch\CreateBranchDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Branch\Dialogs\RenameBranchDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Changesets\ChangesetListViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Changesets\ChangesetsListHeaderState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Changesets\ChangesetsListView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Changesets\ChangesetsSelection.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Changesets\ChangesetsTab.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Changesets\ChangesetsTab_Operations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Changesets\ChangesetsViewMenu.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Changesets\DateFilter.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Changesets\LaunchDiffOperations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\ConfirmContinueWithPendingChangesDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\CreateWorkspace\CreateWorkspaceView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\CreateWorkspace\CreateWorkspaceViewState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\CreateRepositoryDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoriesListHeaderState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoriesListView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoryExplorerDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\CreateWorkspace\Dialogs\RepositoryListViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\CreateWorkspace\DrawCreateWorkspaceView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\CreateWorkspace\ValidRepositoryName.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Diff\ChangeCategoryTreeViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Diff\ClientDiffTreeViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Diff\Dialogs\GetRestorePathDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Diff\DiffPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Diff\DiffSelection.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Diff\DiffTreeView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Diff\DiffTreeViewMenu.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Diff\GetClientDiffInfos.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Diff\MergeCategoryTreeViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Diff\UnityDiffTree.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\DownloadPlasticExeWindow.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\FileSystemOperation.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\History\HistoryListHeaderState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\History\HistoryListView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\History\HistoryListViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\History\HistoryListViewMenu.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\History\HistorySelection.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\History\HistoryTab.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\History\SaveAction.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\ChangeCategoryTreeViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\ChangeTreeViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\DirectoryConflicts\ConflictResolutionState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\DirectoryConflicts\DrawDirectoryResolutionPanel.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesSelection.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTab.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTreeHeaderState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesTreeView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\IncomingChangesViewMenu.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\IsCurrent.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\IsResolved.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Developer\UnityIncomingChangesTree.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\DrawIncomingChangesOverview.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\ChangeCategoryTreeViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\ChangeTreeViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorListViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorsListHeaderState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\Errors\ErrorsListView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesSelection.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTab.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTreeHeaderState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesTreeView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\IncomingChangesViewMenu.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\Gluon\UnityIncomingChangesTree.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\IncomingChanges\IIncomingChangesTab.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\ChangeCategoryTreeViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\ChangeTreeViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\Dialogs\CheckinConflictsDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\Dialogs\CheckinMergeNeededDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\Dialogs\DependenciesDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\Dialogs\EmptyCheckinMessageDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\Dialogs\FilterRulesConfirmationDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\Dialogs\LaunchCheckinConflictsDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\Dialogs\LaunchDependenciesDialog.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\DrawCommentTextArea.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\FilesFilterPatternsMenuBuilder.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\PendingChangesMultiColumnHeader.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\PendingChangesSelection.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTab.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTab_Operations.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTreeHeaderState.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\PendingChangesTreeView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\PendingChangesViewMenu.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\PendingMergeLinks\MergeLinkListViewItem.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\PendingMergeLinks\MergeLinksListView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\PendingChanges\UnityPendingChangesTree.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Welcome\DownloadAndInstallOperation.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Welcome\GetInstallerTmpFileName.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Welcome\MacOSConfigWorkaround.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\Views\Welcome\WelcomeView.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\WebApi\ChangesetFromCollabCommitResponse.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\WebApi\CredentialsResponse.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\WebApi\CurrentUserAdminCheckResponse.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\WebApi\IsCollabProjectMigratedResponse.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\WebApi\OrganizationCredentials.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\WebApi\TokenExchangeResponse.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\WebApi\WebRestApiClient.cs
E:\project\DogBlast_new\Library\PackageCache\com.unity.collab-proxy@1.14.18\Editor\PlasticSCM\WorkspaceWindow.cs
