// dnSpy decompiler from Assembly-CSharp.dll class: Assets.Scripts.Dialogs.WinDialog
using Assets.Scripts.Backend;
using Assets.Scripts.Backend.Commands;
using Assets.Scripts.CasualTools.Common.Logging;
using Assets.Scripts.CasualTools.Dialogs;
using Assets.Scripts.DAO;
using Assets.Scripts.DAO.Entity;
using Assets.Scripts.DataHelpers;
using Assets.Scripts.GamePlayScene;
using Assets.Scripts.GamePlayScene.Mechanics;
using Assets.Scripts.GamePlayScene.Touches;
using Assets.Scripts.GamePlayScene.Tutorials;
using Assets.Scripts.Logging;
using Assets.Scripts.SceneTransitions;
using Assets.Scripts.Utils;
using Assets.Scripts.Utils.Analytics;
using Assets.Scripts.Xiaoming.UI;
using DAO;
using DataHelpers;
using DG.Tweening;
using Dialogs.BuyCoins;
using EventsManagers;
using I2.Loc;
using LevelLoaderScene;
using MapScene;
using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.SocialPlatforms;
using Utils;
using Utils.Analytics;
using static ICSharpCode.SharpZipLib.Zip.ExtendedUnixData;

namespace Assets.Scripts.Dialogs
{
    /// <summary>
    /// 胜利对话框类 - 处理关卡胜利后的所有逻辑
    /// 包括星级显示、奖励发放、购买流程、数据统计等
    /// </summary>
    public class WinDialog : SimpleDialog
    {
        #region 公共字段
        /// <summary>商店包入口组件</summary>
        public BundleEntry bundleEntry;
        /// <summary>其他UI元素的变换组件</summary>
        public Transform othersTf;
        /// <summary>商品价格字符串</summary>
        public string price;
        #endregion

        #region 初始化
        /// <summary>
        /// 对话框启动时的初始化
        /// 设置商店包显示、价格获取等
        /// </summary>
        private void Start()
        {
            // 检查是否已购买折扣包或关卡数不足5关，如果是则隐藏商店包入口
            if (UPlayerUtils.GetInt("DiscountBundleBuyed", 0) == 1 || LevelHelper.Instance.GetHighestLevelUserWon() < 5)
            {
                bundleEntry.gameObject.SetActive(false);
                //othersTf.localPosition = new Vector3(0, -2.58f, 0);
            }
            //else
            //{
            //}
            // 设置其他UI元素位置
            othersTf.localPosition = new Vector3(0, -3f, 0);

            // 去除SDK by:oldP 2025年6月18日10:50:47
            //// 获取商店商品详情和价格信息
            //SkuDetails skuDetails = GMGSDK.getChargeDetailImmediate(13);
            //StoreData bundlePackage = StoreManager.Instance.GetStoreData(StoreManager.Instance._packageStoreItems[7]);
            //string p = skuDetails.mPrice;
            ////优先使用SDK价格，否则使用本地价格
            //price = skuDetails == null ? bundlePackage.GetPrice() : skuDetails.getDisplayPrice();
            // 设置商店包内容和购买回调
            //bundleEntry.SetContent(bundlePackage, price, this.StartPurchase);

            // 如果忽略支付功能，则隐藏商店包入口
            if (GameVersionManager.Instance.IGNORE_PAYMENT)
            {
                bundleEntry.gameObject.SetActive(false);
            }
        }
        #endregion

        #region 购买流程处理
        /// <summary>
        /// 开始购买流程
        /// </summary>
        /// <param name="bundleId">要购买的商品包ID</param>
        private void StartPurchase(int bundleId)
        {
            // 检查网络连接
            if (!NetworkHelper.IsThereConnection())
            {
                DialogManager.Instance.ShowDialog(DialogLibrary.Instance.NoConnectionDialog, false, false, false, null, true);
                return;
            }
            //this.CurrentBundleContent.DisableColliders();
            // 显示加载界面
            LoadingScreenDisplayer.Instance.ShowLoading(LoadingType.Loading, null, null);
            //FacebookEvents.SendInitiatedCheckout();
            //CaravanBillingHelper.Instance.StartPurchase(bundleId, new Action<bool, string>(this.AfterPurchaseCompleted));

            // 执行购买操作
            StoreManager.Instance.GetStoreData(bundleId).Buy(new Action<bool, int>(this.AfterPurchaseCompleted));
        }

        /// <summary>
        /// 购买完成后的回调处理
        /// </summary>
        /// <param name="result">购买是否成功</param>
        /// <param name="packageId">购买的商品包ID</param>
        private void AfterPurchaseCompleted(bool result, int packageId)
        {
            // 隐藏加载界面
            LoadingScreenDisplayer.Instance.StartFadeOut(null, false);
            //this.CurrentBundleContent.EnableColliders();

            if (result) // 购买成功
            {
                //BundlePackage bundlePackage = SalesData.Bundles.FirstOrDefault((BundlePackage p) => p.BundleId.Equals(packageId));
                // 获取购买的商品包数据
                StoreData bundlePackage = StoreManager.Instance.GetStoreData(packageId);
                // 领取所有奖励
                StoreManager.Instance.CliamAllRewards(bundlePackage);

                // 等待加载界面消失后显示奖励获得对话框
                base.StartCoroutine(this.WaitForLoadingScreen(delegate
                {
                    //DialogLibrary.Instance.ShowDialogBackgroundFast();
                    //this.OnPurchaseResultDisplaySuccessed();
                    // 显示物品获得对话框
                    GameObject obj = DialogManager.Instance.ShowDialog(DialogLibrary.Instance.ItemGetDialog, false, false, false, null, true);
                    ItemGetDialog itemGetDialog = obj.GetComponent<ItemGetDialog>();
                    itemGetDialog.SetRewards(StoreManager.Instance.GetRewardDatas(bundlePackage._rewardItemdatas));
                    //itemGetDialog.c
                    //GameObject gameObject = DialogManager.Instance.ShowDialog(DialogLibrary.Instance.PurchaseSuccessDialog, false, false, false, null, false);
                    //PurchaseSuccessDialog component = gameObject.GetComponent<PurchaseSuccessDialog>();
                    //component.Init(package, new Action<InventoryItemType>(this.OnPurchaseResultDisplaySuccessed));
                }, result));

                // 保存已购买折扣包的标记
                UPlayerUtils.Save("DiscountBundleBuyed", 1);
                // 隐藏商店包入口并调整UI位置
                bundleEntry.gameObject.SetActive(false);
                othersTf.localPosition = new Vector3(0, -2.58f, 0);
                //AfterPurchaseCompletedCall?.Invoke();
            }
            else // 购买失败
            {
                // 等待加载界面消失后恢复对话框背景
                base.StartCoroutine(this.WaitForLoadingScreen(delegate
                {
                    DialogLibrary.Instance.ShowDialogBackgroundFast();
                }, result));
            }
        }
        #endregion

        #region 辅助方法
        /// <summary>
        /// 等待加载界面消失的协程
        /// </summary>
        /// <param name="onComplete">完成后的回调</param>
        /// <param name="result">操作结果</param>
        /// <returns></returns>
        private IEnumerator WaitForLoadingScreen(Action onComplete, bool result)
        {
            // 等待加载界面消失
            while (LoadingScreenDisplayer.Instance.IsLoadingBeingDiplayed)
            {
                yield return null;
            }

            // 执行完成回调
            if (onComplete != null)
            {
                onComplete();
            }

            // 如果操作成功，等待当前对话框激活后关闭
            if (result)
            {
                while (DialogManager.Instance.GetActiveDialog() != this)
                {
                    yield return null;
                }
                this.Close();
            }
            yield break;
        }
        #endregion

        #region 对话框生命周期
        /// <summary>
        /// 对话框创建时的初始化
        /// 设置UI状态、广告显示、适配等
        /// </summary>
        public override void DialogCreated()
        {
            base.DialogCreated();

            // 增加助推器点击阻塞请求
            BoosterTapListener.Instance.IncreaseBlockRequests();

            // 初始化时隐藏所有星星
            for (int i = 0; i < this.Stars.Length; i++)
            {
                this.Stars[i].SetActive(false);
            }

            //if (this.ShowHigscoresPanel)
            //{
            //    GameObject gameObject = UnityEngine.Object.Instantiate<GameObject>(this.HighScoresPanelReference, Vector3.zero, Quaternion.identity);
            //    this._highScoresPanel = gameObject.GetComponent<HighScoresPanel>();
            //    this._highScoresPanel.CurrentDialog = this;
            //}

            // 关卡5及以上显示广告
            if (LevelBuilder.CurrentLevelNo >= 5)
                ADControl.instance.ShowAD(3, null);

            // 设置大包显示标记
            MegaBundleDisplay.winGame = true;

            //// 第4关显示评论按钮
            //if (LevelBuilder.CurrentLevelNo == 4)
            //{
            //    CommentButtonTransform.gameObject.SetActive(true);
            //}

            // iPad适配：如果显示了横幅广告，调整对话框缩放和位置
            if (CameraHelper.IsIpad && InitGameSet.beHasShowBanner)
            {
                ScalableAnimationTransform.localScale = Vector3.one * 0.8f;
                ScalableAnimationTransform.localPosition = new Vector3(0, -1, 0);
            }
            else
            {
                ScalableAnimationTransform.localScale = Vector3.one;
                ScalableAnimationTransform.localPosition = Vector3.zero;
            }
        }
        #endregion

        #region 目标和胜利处理
        /// <summary>
        /// 准备并显示关卡目标
        /// </summary>
        /// <param name="goals">关卡目标列表</param>
        /// <param name="levelNo">关卡编号</param>
        public void PrepareGoals(List<Goal> goals, int levelNo)
        {
            int count = goals.Count;
            // 计算目标图标的水平偏移量
            float num = (float)(count - 1) * 0.6f;

            for (int i = 0; i < this.Goals.Length; i++)
            {
                if (i < count) // 有效目标
                {
                    Goal goal = goals[i];
                    // 获取目标类型对应的图标
                    Sprite goalSpriteByItemType = SharedImageLibrary.Instance.GetGoalSpriteByItemType(goal.Type);
                    this.GoalIcons[i].sprite = goalSpriteByItemType;
                    // 设置图标缩放
                    this.GoalIcons[i].transform.localScale = this.GoalIcons[i].transform.localScale * SharedImageLibrary.Instance.GoalImageScale(goal.Type);
                    // 设置完成状态图标（勾选）
                    this.StatusIcons[i].sprite = this.CheckSprite;
                    // 计算并设置目标位置
                    Vector3 localPosition = this.Goals[i].transform.localPosition;
                    localPosition.x = -num + (float)i * 1.2f;
                    this.Goals[i].transform.localPosition = localPosition;
                }
                else // 超出目标数量的UI元素隐藏
                {
                    this.Goals[i].SetActive(false);
                }
            }
        }

        /// <summary>
        /// 处理关卡胜利逻辑
        /// 包括星级计算、数据保存、动画播放、奖励发放等
        /// </summary>
        /// <param name="level">胜利的关卡</param>
        public void Win(Level level)
        {
            // 发送游戏结束日志
            USDKUtils.ULogSender.SendGameOverLog(true, SDKDataExtra.reliveCount, 0, ScoreManager.Score, 0, 0);
            // 设置底部按钮
            SetBottonButtons();

            // 保存当前关卡信息
            this.CurrentLevel = level;
            // 重置关卡尝试次数
            LevelBuilder.ResetLevelTryCount();
            // 准备目标显示
            this.PrepareGoals(this.CurrentLevel.Goals, this.CurrentLevel.LevelNo);
            // 初始化动画序列列表
            this._sequences = new List<Sequence>();

            // 计算获得的星级数量
            int num = 0;
            int score = ScoreManager.Score;
            for (int i = 0; i < level.Stars.Length; i++)
            {
                if (score >= level.Stars[i])
                {
                    num = i + 1;
                }
            }

            // 获取关卡相关数据
            int levelNo = level.LevelNo;
            LevelEntity levelEntity = LevelHelper.Instance.GetLevelEntity(levelNo);
            int highestLevelUserWon = LevelHelper.Instance.GetHighestLevelUserWon();
            int levelNo2 = ChampionsLeagueHelper.Instance.LevelNo;
            bool flag = levelEntity.Score == 0; // 是否首次通关
            int num2 = 0;

            // 设置标题文本
            if (LevelBuilder.LastLevel.IsClLevel) // 冠军联赛关卡
            {
                num2 = ChampionsLeagueHelper.Instance.MoveCount;
                this.TitleLabel.text = ChampionsLeagueHelper.Instance.StageString;
            }
            else // 普通关卡
            {
                int num3 = (LevelLoaderController.FakeLevelNo < 0) ? levelNo : LevelLoaderController.FakeLevelNo;
                this.TitleLabel.text = ScriptLocalization.Get("Level") + num3;
            }
            // 处理非在线关卡的数据保存和奖励
            if (!LevelLoaderController.IsOnlineLevel && !LevelLoaderController.IsOfflineLevel)
            {
                if (flag) // 首次通关
                {
                    // 如果是新通关的关卡，标记为已胜利
                    if (levelEntity.LevelNo >= highestLevelUserWon)
                    {
                        LevelBuilder.LastLevel.Won(num);
                    }
                    // 发送关卡结束事件
                    AdjustHelper.SendLevelEndEvent(levelNo);
                }

                // 检查是否获得了更多星星
                bool flag2 = levelEntity.Stars < num;

                if (ChampionsLeagueHelper.Instance.IsInChampionsLeague) // 冠军联赛模式
                {
                    LevelBuilder.LastLevel.Won(num);
                    flag2 = true;
                    ChampionsLeagueHelper.Instance.IncreaseLevelIndex(num);
                }
                else if (LevelHelper.Instance.GetHighestLevelUserWon() < levelNo) // 新通关关卡
                {
                    // 保存关卡进度和分数
                    LevelHelper.Instance.SaveOrUpdateLevel(levelNo, score, num);
                    // 发送排行榜数据
                    RankManager.Instance.SendRankData(levelNo + 1/* - LevelHelper.Instance.GetHighestLevelUserWon()*/);
                }

                // 处理星星宝箱奖励
                if (ChestHelper.Instance.StarChestAvailable() && flag2)
                {
                    int itemAmount = InventoryHelper.Instance.GetItemAmount(InventoryItemType.Stars);
                    if (itemAmount < ChestHelper.StarChestCollectCount)
                    {
                        // 计算新获得的星星数量
                        int num4 = (!ChampionsLeagueHelper.Instance.IsInChampionsLeague) ? (num - levelEntity.Stars) : num;
                        // 添加星星到背包
                        InventoryHelper.Instance.AddItemAmount(InventoryItemType.Stars, num4, false);
                        ChestHelper.Instance.StarCollectCount = num4;
                    }
                }
            }
            //if (this._highScoresPanel != null)
            //{
            //    this._highScoresPanel.Init(levelNo, 1);
            //}
            LogManager.Debug(LogTags.WinDialog, "Won Level:{0} Score:{1} Stars:{2} Name:{3}", new object[]
            {
                levelNo,
                score,
                num,
                level.Name
            });
            this._winSequence = DOTween.Sequence();
            this._winSequence.AppendInterval(0.2f);
            Vector3[] array = new Vector3[]
            {
                new Vector3(-1.76f, 8.55f, 0f),
                new Vector3(0f, 8.69f, 0f),
                new Vector3(2.37f, 8.4f, 0f)
            };
            Vector3[] array2 = new Vector3[]
            {
                new Vector3(0.014f, -0.154f, 0f),
                new Vector3(0f, -0.089f, 0f),
                new Vector3(-0.056f, -0.121f, 0f)
            };
            Vector3[] array3 = new Vector3[]
            {
                new Vector3(0f, -0.015f, 0f),
                new Vector3(0f, 0f, 0f),
                new Vector3(0.009f, -0.023f, 0f)
            };
            Vector3 localScale = Vector3.one * 5f;
            Vector3 endValue = Vector3.one * 0.9f;
            Vector3 endValue2 = Vector3.one * 1.05f;
            Vector3 one = Vector3.one;
            for (int j = 0; j < num; j++)
            {
                GameObject star = this.Stars[j];
                ParticleSystem @object = this.StarParticles[j];
                MeshRenderer @mesh = this.MeshRenderersStar[j];
                Sequence sequence = DOTween.Sequence();
                star.transform.localScale = localScale;
                star.transform.localPosition = array[j];
                int x = j;
                sequence.AppendCallback(delegate
                {
                    star.SetActive(true);
                });
                sequence.Append(star.transform.DOScale(endValue, 0.3f).SetEase(Ease.InCubic));
                sequence.Join(star.transform.DOLocalMove(array2[j], 0.3f, false).SetEase(Ease.InCubic));
                sequence.AppendCallback(delegate
                {
                    if (x != 1)
                    {
                        if (x != 2)
                        {
                            AudioManager.Play(AudioTag.UiPopupStar1, Assets.Scripts.Utils.PlayMode.Frame, null, 1f);
                        }
                        else
                        {
                            AudioManager.Play(AudioTag.UiPopupStar3, Assets.Scripts.Utils.PlayMode.Frame, null, 1f);
                        }
                    }
                    else
                    {
                        AudioManager.Play(AudioTag.UiPopupStar2, Assets.Scripts.Utils.PlayMode.Frame, null, 1f);
                    }
                });
                sequence.Append(star.transform.DOScale(endValue2, 0.08f).SetEase(Ease.Linear));
                sequence.Join(star.transform.DOLocalMove(array3[j], 0.08f, false).SetEase(Ease.Linear));
                sequence.Append(star.transform.DOScale(one, 0.06f).SetEase(Ease.Linear));
                sequence.InsertCallback(0.3f, new TweenCallback(() =>
                {
                    @mesh.sortingLayerName = "Dialogs";
                    @mesh.sortingOrder = 60;
                    @object.Play();
                }));
                this._sequences.Add(sequence);
                this._winSequence.Insert(0.6f + 0.3f * (float)j, sequence);
            }
            this._winSequence.AppendInterval(0.2f);
            this._winSequence.AppendCallback(new TweenCallback(this.StartShines));
            this._winSequence.OnComplete(delegate
            {
                this._winSequence = null;
            });
            WinDialog.UpdateEventData(num);
            JsonLevelEndInventory jsonLevelEndInventory = new JsonLevelEndInventory();
            jsonLevelEndInventory.inventory = new JsonInventory();
            jsonLevelEndInventory.inventory.FillInventoryData();
            jsonLevelEndInventory.FillExtraData();
            this._levelEndData = new JsonLevelEndData
            {
                user_id = UserIdHelper.Instance.UserId.ToString(),
                level = ((!LevelBuilder.LastLevel.IsClLevel) ? this.CurrentLevel.LevelNo : (-1 * this.CurrentLevel.LevelNo)),
                level_name = this.CurrentLevel.Name,
                level_max = ((!flag) ? (LevelHelper.Instance.GetHighestLevelUserWon() + 1) : this.CurrentLevel.LevelNo),
                number_of_ego_shown = this.CurrentLevel.EgoDialogIndex,
                result = num,
                user_score = score,
                moves_given = this.CurrentLevel.LevelMoves + num2 + this.CurrentLevel.CurrentLevelBuilder.LevelMoveABCount(),
                moves_made = this.CurrentLevel.MaximumMovesAllowed - this.CurrentLevel.MovesLeftBeforeWin,
                duration = (int)(Time.realtimeSinceStartup - this.CurrentLevel.StartTime),
                min_fps = this.CurrentLevel.CurrentLevelBuilder.FPSMeter.MinimumFps().ToString(),
                max_fps = this.CurrentLevel.CurrentLevelBuilder.FPSMeter.MaximumFps().ToString(),
                avg_fps = this.CurrentLevel.CurrentLevelBuilder.FPSMeter.AverageFps().ToString(),
                under_50_fps = this.CurrentLevel.CurrentLevelBuilder.FPSMeter.SpikeCount(),
                data = jsonLevelEndInventory,
                cl_stage = levelNo2
            };
            this._levelEndData.FillChampionsLeagueData();
            if (!LevelBuilder.LastLevel.IsClLevel)
            {
                int num5 = levelNo + 1;
                switch (num5)
                {
                    case 7:
                        InventoryHelper.Instance.AddItemAmount(InventoryItemType.Rocket, 3, false);
                        InventoryHelper.Instance.AddItemAmount(InventoryItemType.Bomb, 3, false);
                        InventoryHelper.Instance.AddItemAmount(InventoryItemType.DiscoBall, 3, false);
                        TutorialManager.ShouldDisplayPrelevelTutorial = true;
                        break;

                    default:
                        switch (num5)
                        {
                            case 17:
                                InventoryHelper.Instance.AddItemAmount(InventoryItemType.Anvil, 4, false);
                                TutorialManager.ShouldDisplayBoosterTutorial = true;
                                break;

                            default:
                                if (num5 == 13)
                                {
                                    InventoryHelper.Instance.AddItemAmount(InventoryItemType.BoxingGlove, 4, false);
                                    TutorialManager.ShouldDisplayBoosterTutorial = true;
                                }
                                break;

                            case 20:
                                InventoryHelper.Instance.AddItemAmount(InventoryItemType.Dice, 4, false);
                                TutorialManager.ShouldDisplayBoosterTutorial = true;
                                break;
                        }
                        break;

                    case 10:
                        InventoryHelper.Instance.AddItemAmount(InventoryItemType.Hammer, 4, false);
                        TutorialManager.ShouldDisplayBoosterTutorial = true;
                        break;
                }
            }

            // 处理通关奖励
            //this.HandleLevelCompletionReward(levelNo);
            isGetReward = false;
            curRewardCoin = InventoryHelper.Instance.HandleLevelCompletionReward(flag);
            this.ShowRewardAction();
            CommandManager.SendLevelWinCommands();
            PlayersLeaderboardDAO.Instance.UpdateCurrentUser();
        }
        #endregion

        #region 生命周期和清理
        /// <summary>
        /// 对象销毁时清理动画序列
        /// </summary>
        public void OnDestroy()
        {
            // 停止胜利动画序列
            if (this._winSequence != null)
            {
                this._winSequence.Kill(false);
            }

            // 停止所有星级动画序列
            for (int i = 0; i < this._sequences.Count; i++)
            {
                Sequence t = this._sequences[i];
                if (t.IsActive())
                {
                    t.Kill(false);
                }
            }
        }
        #endregion

        #region 事件和动画处理
        /// <summary>
        /// 更新游戏事件数据
        /// </summary>
        /// <param name="stars">获得的星级数</param>
        private static void UpdateEventData(int stars)
        {
            GeneralEventManager instance = GeneralEventManager.Instance;

            // 更新皇冠冲刺事件
            CrownRushEventManager crownRush = instance.CrownRush;
            if (crownRush != null)
            {
                crownRush.LevelWin();
            }

            // 更新团队宝箱事件
            TeamChestEventManager teamChest = instance.TeamChest;
            if (teamChest != null)
            {
                teamChest.LevelWin(stars);
            }
        }

        /// <summary>
        /// 开始星星闪烁动画
        /// </summary>
        private void StartShines()
        {
            if (this.Stars == null)
            {
                return;
            }

            // 为每个激活的星星添加滑动闪烁效果
            for (int i = 0; i < this.Stars.Length; i++)
            {
                if (this.Stars[i].activeSelf)
                {
                    StarSlider starSlider = this.Stars[i].AddComponent<StarSlider>();
                    starSlider.SetOrder(i);
                    starSlider.CanPlay = true;
                }
            }
        }
        #endregion

        #region 按钮事件处理
        /// <summary>
        /// 继续按钮点击事件
        /// 加速动画并关闭对话框
        /// </summary>
        public void ContinueButton()
        {
            // 如果胜利动画正在播放，加速播放
            if (this._winSequence != null)
            {
                this._winSequence.timeScale = 50f;
            }
            this.Close();
        }

        /// <summary>
        /// 评论按钮点击事件
        /// 显示评分对话框
        /// </summary>
        public void CommentButton()
        {
            // 隐藏教程提示
            Transform t = CommentButtonTransform.Find("Tut");
            if (t != null)
            {
                t.gameObject.SetActive(false);
            }

            //GMGSDK.jumpToGP();//����ĳɴ򿪽���ȥ����
            // 显示评分对话框
            DialogManager.Instance.ShowDialog(DialogLibrary.Instance.RateUsDialog, false, false, false, null, true).GetComponent<RateUsDialog>().OnClose = delegate ()
            {
                Debug.LogError("�����۽���");
            };
        }
        #endregion

        #region 对话框控制
        /// <summary>
        /// 关闭对话框并处理场景切换
        /// </summary>
        public override void Close()
        {
            if (LevelLoaderController.IsOnlineLevel || LevelLoaderController.IsOfflineLevel)
            {
                // 在线关卡或离线关卡，返回关卡加载器
                CaravanSceneManager.Load(Scenes.LevelLoader, null);
            }
            else
            {
                if (this.CurrentLevel.LevelNo == 1 && LevelHelper.Instance.GetHighestLevelUserWon() == 1)
                {
                    // 第一关通关后自动进入第二关
                    LevelBuilder.ShouldRemoveBackgroundImagesOnDestroy = false;
                    LevelBuilder.CurrentLevelNo = 2;
                    LevelBuilder.LevelStartType = "Automatic";
                    CaravanSceneManager.Load(Scenes.GamePlay, null);
                }
                else
                {
                    // 其他情况返回地图场景
                    CaravanSceneManager.Load(Scenes.Map, null);
                }
                // 发送关卡结束事件数据
                EventSender.SendLevelEndEvent(this._levelEndData);
                // 发送推送标签
                OneSignal.SendTag("LevelTag", (this.CurrentLevel.LevelNo + 1).ToString());
            }
        }

        /// <summary>
        /// 播放对话框退出动画
        /// </summary>
        /// <returns>动画序列</returns>
        public override Sequence PlayDialogOutAnimation()
        {
            //if (this._highScoresPanel != null)
            //{
            //    this._highScoresPanel.MoveOut();
            //}
            return base.PlayDialogOutAnimation();
        }

        /// <summary>
        /// 播放对话框进入动画
        /// </summary>
        public override void PlayDialogInAnimation()
        {
            base.PlayDialogInAnimation();
            if (!base.gameObject.activeSelf)
            {
                base.gameObject.SetActive(true);
            }
            //if (this._highScoresPanel != null)
            //{
            //    base.StartCoroutine(this.MoveHighScoresIn());
            //}
        }

        //private IEnumerator MoveHighScoresIn()
        //{
        //    //this._highScoresPanel.RefreshHighscorePanel();
        //    //yield return new WaitForSeconds(0.35f);
        //    //this._highScoresPanel.MoveIn(HighPanelOpereateType.WinDialog);
        //    //yield break;
        //}

        /// <summary>
        /// 是否可以通过点击黑色面板关闭对话框
        /// </summary>
        /// <returns>false - 不允许</returns>
        public override bool CanCloseOnBlackPanelTouch()
        {
            return false;
        }

        /// <summary>
        /// 场景切换时是否应该关闭对话框
        /// </summary>
        /// <returns>false - 不关闭</returns>
        public override bool ShouldBeClosedOnSceneChange()
        {
            return false;
        }
        #endregion

        #region 视频奖励
        /// <summary>
        /// 展示当前奖励的动画信息 TODO:需要制作奖励动画
        /// </summary>
        private void ShowRewardAction()
        {
            this.Score.text = curRewardCoin.ToString();//ScriptLocalization.Get("Score") + string.Format(": <color=#CA2A39>{0}</color>", score);
            int coin = InventoryHelper.Instance.GetItemAmount(InventoryItemType.NewCoin);
            int minWithdrawCoins = GameVersionManager.Instance.GetDifferenceCoins(coin);
            ProgressLabel.text = coin + "/" + minWithdrawCoins;
            RemainingLabel.text = string.Format(ScriptLocalization.Get("Remaining"), minWithdrawCoins - coin);
            Progress.material.SetFloat("_Progress", (float)coin / minWithdrawCoins);
        }
        public void WinWatchVideoNewCoin()
        {
            if (isGetReward)
            {
                ContinueButton();
                return;
            }
            ADControl.instance.ShowWatchVideo(WatchVideoType.SettlementCoin, WatchVideoSite.WinPage, "Coins2", 30, (bool isSuccess) =>
            {
                if (isSuccess) // 观看成功
                {
                    curRewardCoin = InventoryHelper.Instance.FiveTimesRewardProcessing(curRewardCoin);
                    ShowRewardAction();
                    isGetReward = true;
                }
            });
        }
        /// <summary>
        /// 观看视频获得金币
        /// </summary>
        public void WinWatchVideoCoin()
        {
            ADControl.instance.ShowWatchVideo(WatchVideoType.SettlementCoin, WatchVideoSite.WinPage, "Coins2", 30, (bool isSuccess) =>
             {
                 //InventoryHelper.Instance.AddItemAmount(InventoryItemType.Coins, 30, true);
                 if (isSuccess) // 观看成功
                 {
                     // 添加30金币到背包
                     InventoryHelper.Instance.AddItemAmount(InventoryItemType.Coins, 30, true);
                     // 创建奖励字典
                     Dictionary<InventoryItemType, int> dicRewards = new Dictionary<InventoryItemType, int>();
                     dicRewards.Add(InventoryItemType.Coins, 30);
                     // 显示物品获得对话框
                     GameObject obj = DialogManager.Instance.ShowDialog(DialogLibrary.Instance.ItemGetDialog, false, false, false, null, true);
                     ItemGetDialog itemGetDialog = obj.GetComponent<ItemGetDialog>();
                     itemGetDialog.SetRewards(dicRewards);
                     //this.AfterPurchaseCompleted(isSuccess, bundleId);
                 }
             });
        }
        #endregion

        #region 按钮设置
        /// <summary>
        /// 设置底部按钮的显示和位置
        /// 目前屏蔽了视频按钮的显示逻辑
        /// </summary>
        private void SetBottonButtons()
        {
            // 屏蔽视频按钮的显示逻辑
            //if (ADControl.instance.GetWatchVideoRemanentTime(WatchVideoType.SettlementCoin) > 0)
            //{
            //    this.VideoButtonTransform.localPosition = new Vector3(-1.90f, -3.2f, 0f);
            //    this.ContinueButtonTransform.localPosition = new Vector3(1.50f, -3.2f, 0f);
            //    if (this.VideoButtonTransform.gameObject.activeSelf == false)
            //    {
            //        this.VideoButtonTransform.gameObject.SetActive(true);
            //    }
            //}
            //else
            //{
            //    //this.VideoButtonTransform.localPosition = new Vector3(-2f, -3.2f, 0f);
            //    this.ContinueButtonTransform.localPosition = new Vector3(-0.3f, -3.2f, 0f);
            //    if (this.VideoButtonTransform.gameObject.activeSelf == true)
            //    {
            //        this.VideoButtonTransform.gameObject.SetActive(false);
            //    }
            //}
        }
        #endregion

        #region UI组件字段
        /// <summary>评论按钮变换组件</summary>
        public Transform CommentButtonTransform;
        /// <summary>视频按钮变换组件</summary>
        public Transform VideoButtonTransform;
        /// <summary>继续按钮变换组件</summary>
        public Transform ContinueButtonTransform;
        /// <summary>高分面板预制体引用</summary>
        public GameObject HighScoresPanelReference;
        /// <summary>分数显示文本</summary>
        public TextMeshPro Score;
        /// <summary>分隔符1</summary>
        public GameObject Seperator1;
        /// <summary>勾选图标</summary>
        public Sprite CheckSprite;
        /// <summary>叉号图标</summary>
        public Sprite CrossSprite;
        /// <summary>进度条</summary>
        public SpriteRenderer Progress;
        /// <summary>金币进度显示文本</summary>
        public TextMeshPro ProgressLabel;
        /// <summary>目标游戏对象数组</summary>
        public GameObject[] Goals;
        /// <summary>星星网格渲染器数组</summary>
        public MeshRenderer[] MeshRenderersStar;
        /// <summary>目标图标精灵渲染器数组</summary>
        public SpriteRenderer[] GoalIcons;
        /// <summary>状态图标精灵渲染器数组</summary>
        public SpriteRenderer[] StatusIcons;
        /// <summary>星星粒子系统数组</summary>
        public ParticleSystem[] StarParticles;
        /// <summary>是否显示高分面板</summary>
        public bool ShowHigscoresPanel;
        /// <summary>星星游戏对象数组</summary>
        public GameObject[] Stars;
        /// <summary>标题标签文本</summary>
        public TextMeshPro TitleLabel;
        /// <summary>剩余奖励说明</summary>
        public TextMeshPro RemainingLabel;
        #endregion

        #region 私有字段
        /// <summary>当前关卡</summary>
        public Level CurrentLevel;
        //private HighScoresPanel _highScoresPanel;
        /// <summary>胜利动画序列</summary>
        private Sequence _winSequence;
        /// <summary>星级动画序列列表</summary>
        private List<Sequence> _sequences;
        /// <summary>关卡结束数据</summary>
        private JsonLevelEndData _levelEndData;
        /// <summary> 当前奖励金币数量 </summary>
        private int curRewardCoin = 0;
        /// <summary> 是否获取过倍数奖励 </summary>
        private bool isGetReward = false;
        #endregion
    }
}