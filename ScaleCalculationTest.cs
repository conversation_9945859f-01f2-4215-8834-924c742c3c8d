using UnityEngine;

/// <summary>
/// 测试弹窗缩放计算的示例类
/// 用于验证为什么设置为1的缩放会变成1.28
/// </summary>
public class ScaleCalculationTest : MonoBehaviour
{
    void Start()
    {
        // 模拟 WinDialog 的参数
        float referenceWidth = 8.58f;      // 普通设备参考宽度
        float ipadReferenceWidth = 10f;    // iPad 参考宽度
        float referenceHeight = 10.6f;     // 参考高度
        
        // 模拟不同的屏幕尺寸
        TestScaling("iPhone (9:16)", 9f, 16f, false, referenceWidth, ipadReferenceWidth, referenceHeight);
        TestScaling("iPad (4:3)", 12f, 16f, true, referenceWidth, ipadReferenceWidth, referenceHeight);
        TestScaling("iPhone X (9:19.5)", 9f, 19.5f, false, referenceWidth, ipadReferenceWidth, referenceHeight);
        
        // 测试一个可能导致1.28缩放的情况
        TestScaling("可能的设备", 10.98f, 16f, false, referenceWidth, ipadReferenceWidth, referenceHeight);
    }
    
    void TestScaling(string deviceName, float width, float height, bool isIpad, 
                    float refWidth, float ipadRefWidth, float refHeight)
    {
        Debug.Log($"\n=== {deviceName} 测试 ===");
        Debug.Log($"屏幕尺寸: {width} x {height}");
        Debug.Log($"宽高比: {width/height:F3}");
        Debug.Log($"是否iPad: {isIpad}");
        
        // 模拟 DialogManager.ScaleDialog 的计算逻辑
        float b; // 基于宽度的缩放因子
        if (isIpad)
        {
            b = width / ipadRefWidth;
        }
        else
        {
            b = width / refWidth;
        }
        
        float a = height / refHeight; // 基于高度的缩放因子
        float finalScale = Mathf.Min(a, b); // 取较小值确保不超出屏幕
        
        Debug.Log($"宽度缩放因子 (b): {b:F3}");
        Debug.Log($"高度缩放因子 (a): {a:F3}");
        Debug.Log($"最终缩放 (Min(a,b)): {finalScale:F3}");
        
        // 检查是否接近1.28
        if (Mathf.Abs(finalScale - 1.28f) < 0.01f)
        {
            Debug.Log("*** 这个配置会产生接近1.28的缩放! ***");
        }
    }
}
