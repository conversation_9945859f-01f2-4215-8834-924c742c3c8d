﻿// dnSpy decompiler from Assembly-CSharp.dll class: Assets.Scripts.Dialogs.SimpleDialog
using Assets.Scripts.CasualTools.Dialogs;
using Assets.Scripts.Utils;
using DG.Tweening;
using System;
using UnityEngine;

namespace Assets.Scripts.Dialogs
{
    /// <summary>
    /// 简单弹窗基类，提供弹窗的动画、碰撞体控制、位置设置等通用功能。
    /// 继承自 Dialog，供具体弹窗类继承使用。
    /// </summary>
    public abstract class SimpleDialog : Dialog
    {
        /// <summary>
        /// 启用所有弹窗上的 BoxCollider2D，使弹窗可交互。
        /// </summary>
        protected void EnableColliders()
        {
            if (this.Colliders == null)
            {
                return;
            }
            for (int i = 0; i < this.Colliders.Length; i++)
            {
                BoxCollider2D boxCollider2D = this.Colliders[i];
                if (boxCollider2D != null)
                {
                    boxCollider2D.enabled = true;
                }
            }
        }

        /// <summary>
        /// 禁用所有弹窗上的 BoxCollider2D，使弹窗不可交互。
        /// </summary>
        protected void DisableColliders()
        {
            if (this.Colliders == null)
            {
                return;
            }
            for (int i = 0; i < this.Colliders.Length; i++)
            {
                BoxCollider2D boxCollider2D = this.Colliders[i];
                if (boxCollider2D != null)
                {
                    boxCollider2D.enabled = false;
                }
            }
        }

        /// <summary>
        /// 弹窗入场动画播放完毕后的回调，子类可重写。
        /// </summary>
        protected virtual void PlayedIn()
        {
        }

        /// <summary>
        /// 弹性缓动函数，用于弹窗动画。
        /// </summary>
        /// <param name="time">当前时间</param>
        /// <param name="duration">总时长</param>
        /// <param name="overshootOrAmplitude">弹性幅度</param>
        /// <param name="period">周期</param>
        /// <returns>插值结果</returns>
        public float EaseOutElastic(float time, float duration, float overshootOrAmplitude, float period)
        {
            float num = time / duration;
            return (Math.Abs(num) >= 1E-06f) ? ((Math.Abs(num - 1f) >= 1E-06f) ? (Mathf.Pow(2f, -10f * num) * Mathf.Sin((num * 10f - 0.75f) * 2.09439516f) + 1f) : 1f) : 0f;
        }

        /// <summary>
        /// 播放弹窗入场动画。
        /// </summary>
        public override void PlayDialogInAnimation()
        {
            this.ActivateDialog(true);
            this.PutMiddle();
            float x = base.gameObject.transform.localScale.x;
            this._inAnimation = DOTween.Sequence();
            this._inAnimation.Append(base.gameObject.transform.DOScale(x + 0.02f, 0.08f * this.t));
            this._inAnimation.Append(base.gameObject.transform.DOScale(x - 0.01f, 0.06f * this.t));
            this._inAnimation.Append(base.gameObject.transform.DOScale(x, 0.04f * this.t));
            this._inAnimation.AppendCallback(new TweenCallback(this.EnableColliders));
            this._inAnimation.AppendCallback(new TweenCallback(this.PlayedIn));
            this._inAnimation.OnComplete(delegate
            {
                this._inAnimation = null;
            });
        }

        /// <summary>
        /// 播放弹窗出场动画。
        /// </summary>
        /// <returns>动画序列（此处返回 null）</returns>
        public override Sequence PlayDialogOutAnimation()
        {
            if (this._inAnimation != null && this._inAnimation.IsPlaying())
            {
                this._inAnimation.Kill(false);
            }
            this.DisableColliders();
            this.ActivateDialog(false);
            return null;
        }

        /// <summary>
        /// 立即关闭弹窗，无动画。
        /// </summary>
        public override void PlayDialogOutFast()
        {
            if (this._inAnimation != null && this._inAnimation.IsPlaying())
            {
                this._inAnimation.Kill(false);
            }
            this.DisableColliders();
            this.ActivateDialog(false);
        }

        /// <summary>
        /// 弹窗创建时的初始化操作。
        /// </summary>
        public override void DialogCreated()
        {
            base.FrameCreated = Time.frameCount;
            this.DisableColliders();
            this.PutMiddle();
        }

        /// <summary>
        /// 设置弹窗的触摸边界。
        /// </summary>
        public override void SetDialogBounds()
        {
            if (this.TouchBounds == null)
            {
                return;
            }
            Vector3 center = new Vector3
            {
                x = base.transform.position.x + this.TouchBounds.Offset.x,
                y = base.transform.position.y + this.TouchBounds.Offset.y,
                z = 0f
            };
            Vector3 size = new Vector3
            {
                x = this.TouchBounds.Size.x * base.transform.localScale.x,
                y = this.TouchBounds.Size.y * base.transform.localScale.y,
                z = 0f
            };
            this.DialogBounds = new Bounds(center, size);
        }

        /// <summary>
        /// 获取弹窗的触摸边界。
        /// </summary>
        /// <returns>弹窗边界</returns>
        public override Bounds GetDialogBounds()
        {
            return this.DialogBounds;
        }

        /// <summary>
        /// 激活或隐藏弹窗。
        /// </summary>
        /// <param name="value">true 显示，false 隐藏</param>
        public void ActivateDialog(bool value)
        {
            base.gameObject.SetActive(value);
        }

        /// <summary>
        /// 将弹窗居中显示，针对不同设备有不同的 Y 轴偏移。
        /// </summary>
        private void PutMiddle()
        {
            float y = (!CameraHelper.IsIpad) ? this.OffsetY : this.IPadOffsetY;
            base.gameObject.transform.position = new Vector3(0f, y, -2f);
        }

        /// <summary>
        /// 弹性动画常量。
        /// </summary>
        private const float C4 = 2.09439516f;

        /// <summary>
        /// 动画时长缩放因子。
        /// </summary>
        private float t = 1.5f;

        /// <summary>
        /// 当前入场动画序列。
        /// </summary>
        private Sequence _inAnimation;

        /// <summary>
        /// 弹窗上的所有 BoxCollider2D 组件。
        /// </summary>
        public BoxCollider2D[] Colliders;
    }
}