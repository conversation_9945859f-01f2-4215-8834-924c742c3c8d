// dnSpy decompiler from Assembly-CSharp.dll class: MapScene.DailyBonusDisplay
using System;
using System.Collections.Generic;
using Assets.Scripts.CasualTools.Dialogs;
using Assets.Scripts.Dialogs;
using DataHelpers;
using UnityEngine;
using Utils;
using System.Linq;
using Assets.Scripts.Backend;
using Assets.Scripts.Billing;
using Assets.Scripts.CasualTools.Common.Logging;
using Assets.Scripts.DataHelpers;
using Assets.Scripts.Logging;
using Assets.Scripts.SceneTransitions;
using Assets.Scripts.Xiaoming.UI;
using Facebook;
using NewMapScene;

namespace MapScene
{
    public class CoinVideoDisplay : MapDisplay
    {

        public void Prepare()
        {
            if (ADControl.instance.CheckWatchVideoShowTimeOver(WatchVideoType.MainCoin))
            {
                base.gameObject.SetActive(false);
            }
            else
            {
                base.gameObject.SetActive(true);
            }
        }

        public void OnClick()
        {
            ADControl.instance.ShowWatchVideo(WatchVideoType.MainCoin, WatchVideoSite.MainPate, "", 10,
                (success) =>
                {
                    if (success)
                    {
                        int reward = InventoryHelper.Instance.HandleSubsequentLevelReward();
                        InventoryHelper.Instance.AddItemAmount(InventoryItemType.NewCoin, reward, true);
                        Dictionary<InventoryItemType, int> dicRewards = new Dictionary<InventoryItemType, int>();
                        dicRewards.Add(InventoryItemType.NewCoin, reward);
                        GameObject obj = DialogManager.Instance.ShowDialog(DialogLibrary.Instance.ItemGetDialog, false, false, false, null, true);
                        ItemGetDialog itemGetDialog = obj.GetComponent<ItemGetDialog>();
                        itemGetDialog.SetRewards(dicRewards);
                        //InventoryHelper.Instance.DelayedCoins += 30;

                        Prepare();
                    }

                });
        }

    }
}