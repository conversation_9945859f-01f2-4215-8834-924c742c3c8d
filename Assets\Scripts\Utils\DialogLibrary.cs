// dnSpy decompiler from Assembly-CSharp.dll class: Utils.DialogLibrary
using System;
using Assets.Scripts.Utils;
using DG.Tweening;
using UnityEngine;

namespace Utils
{
    public class DialogLibrary : MonoBehaviour
    {
        public void Awake()
        {
            DialogLibrary.Instance = this;
        }

        public void Start()
        {
            this.DialogBackground.transform.localScale = new Vector3(CameraHelper.Width, CameraHelper.Height, 1f);
        }

        private void OnDestroy()
        {
            DialogLibrary.Instance = null;
        }

        public void ShowDialogBackgroundFast()
        {
            if (this._isBackgroundActive)
            {
                return;
            }
            this._isBackgroundActive = true;
            if (this._backgroundFadeTween != null && this._backgroundFadeTween.IsPlaying())
            {
                this._backgroundFadeTween.Kill(false);
            }
            this.DialogBackground.enabled = true;
            this.DialogBackground.color = new Color(0f, 0f, 0f, 0.8f);
        }

        public void ShowDialogBackground(bool lighter = false, float duration = 0.12f, float delay = 0f)
        {
            if (this._isBackgroundActive)
            {
                return;
            }
            this._isBackgroundActive = true;
            if (this._backgroundFadeTween != null && this._backgroundFadeTween.IsPlaying())
            {
                this._backgroundFadeTween.Kill(false);
            }
            else
            {
                this.DialogBackground.color = new Color(0f, 0f, 0f, 0f);
            }
            this.DialogBackground.enabled = true;
            this._backgroundFadeTween = this.DialogBackground.DOColor(new Color(0f, 0f, 0f, (!lighter) ? 0.8f : 0.2f), duration).SetDelay(delay).OnComplete(delegate
            {
                this._backgroundFadeTween = null;
            });
        }

        public void DarkenBackground()
        {
            this.DialogBackground.DOColor(new Color(0f, 0f, 0f, 0.89f), 0.12f);
        }

        public void HideDialogBackground()
        {
            if (!this._isBackgroundActive)
            {
                return;
            }
            this._isBackgroundActive = false;
            if (this._backgroundFadeTween != null && this._backgroundFadeTween.IsPlaying())
            {
                this._backgroundFadeTween.Kill(false);
            }
            this._backgroundFadeTween = this.DialogBackground.DOColor(new Color(0f, 0f, 0f, 0f), 0.12f);
            this._backgroundFadeTween.OnComplete(delegate
            {
                this.DialogBackground.enabled = false;
                this._backgroundFadeTween = null;
            });
        }

        public static DialogLibrary Instance { get; private set; }

        private const float FadeDuration = 0.12f;

        private Tween _backgroundFadeTween;

        private bool _isBackgroundActive;

        public SpriteRenderer DialogBackground;

        public GameObject BuyCoinsBundleDialog;

        public GameObject NoMoneyDialog;

        public GameObject PurchaseSuccessDialog;

        public GameObject PurchaseFailedDialog;

        public GameObject PurchaseRetryCompletedDialog;

        public GameObject[] EgoDialogs;

        //public GameObject FacebookConnectedDialog;

        //public GameObject FacebookConnectFailedDialog;

        //public GameObject FacebookLogoutFailedDialog;

        //public GameObject FacebookLogoutSuccededDialog;

        //public GameObject FacebookLogoutConfirmationDialog;

        //public GameObject FacebookInviteFriendsDialog;

        public GameObject GoalsBannerDialog;

        public GameObject QuitDialog;

        public GameObject LostDialog;

        public GameObject WinDialog;

        public GameObject BuyResourcesDialog;

        public GameObject PaintBrushDialog;

        public GameObject PrelevelDialog;

        public GameObject NoLivesLeftGamePlayDialog;

        public GameObject NoLivesLeftMapDialog;

        public GameObject NoConnectionDialog;

        public GameObject LinkDeviceDialog;

        public GameObject LinkDeviceEnterPinDialog;

        public GameObject LinkDeviceResultDialog;

        public GameObject LinkDeviceShowPinDialog;

        public GameObject SupportTicketDialog;

        public GameObject SupportFailedDialog;

        public GameObject SupportSuccessDialog;

        public GameObject BoosterUnlockedDialog;

        public GameObject ItemGetDialog;

        public GameObject ToonChestDialog;

        public GameObject StarChestDialog;

        public GameObject MoreLevelsDialog;

        public GameObject ChestInfoDialog;

        public GameObject StarChestInfoDialog;

        public GameObject RateUsDialog;

        public GameObject SocialCreateUserDialog;

        public GameObject SocialLeaveTeamDialog;

        public GameObject SocialChangeTeamDialog;

        public GameObject SocialTeamInfoJoinDialog;

        public GameObject SocialSelectBadgeDialog;

        public GameObject SocialConfirmKickDialog;

        public GameObject SocialTeamInfoDialog;

        public GameObject SettingsDialog;

        public GameObject TeamEditDialog;

        public GameObject NewLevelsDialog;

        public GameObject EpisodeNavigationDialog;

        public GameObject EpisodeUnlockedDialog;

        public GameObject ChatReportConfirmationDialog;

        public GameObject CLPrelevelDialog;

        public GameObject CLEnterenceDialog;

        public GameObject CLLeaderboardDialog;

        public GameObject CLRewardsDialog;

        public GameObject CLWinDialog;

        public GameObject CLLostDialog;

        public GameObject LifeHackDialog;

        public GameObject CrownRushInfoDialog;

        public GameObject DailyBonusDialog;

        public GameObject TeamChestInfoDialog;

        public GameObject TeamChestJoinTeamDialog;

        public GameObject TeamChestDialog;

        public GameObject ChangeNameUserDialog;

        public GameObject StarTournamentInfoDialog;

        public GameObject StarTournamentResultDialog;

        public GameObject StarTournamentRewardsDialog;

        public GameObject StarTournamentCreateUserDialog;

        public GameObject StarTournamentAnnouncementDialog;

        public GameObject TeamTournamentAnnouncementDialog;

        public GameObject TeamTournamentInfoDialog;

        public GameObject TeamTournamentRewardDialog;

        public GameObject SevenDaysBountsDialog;

        public GameObject ExchangeCDKeyDialog;

        public GameObject DiscountBundleDialog;

        public GameObject LuckwheelDialog;
    }
}