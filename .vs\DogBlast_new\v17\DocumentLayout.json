{"Version": 1, "WorkspaceRootPath": "E:\\project\\DogBlast_new\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|E:\\project\\DogBlast_new\\assets\\scripts\\mapscene\\activitybutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|solutionrelative:assets\\scripts\\mapscene\\activitybutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|e:\\project\\dogblast_new\\assets\\scripts\\mapscene\\coinvideodisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|solutionrelative:assets\\scripts\\mapscene\\coinvideodisplay.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|E:\\project\\DogBlast_new\\assets\\scripts\\_syl\\common\\gameversionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|solutionrelative:assets\\scripts\\_syl\\common\\gameversionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|E:\\project\\DogBlast_new\\assets\\scripts\\assets_scripts_datahelpers\\inventoryhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|solutionrelative:assets\\scripts\\assets_scripts_datahelpers\\inventoryhelper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|E:\\project\\DogBlast_new\\assets\\scripts\\assets_scripts_dialogs\\windialog.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|solutionrelative:assets\\scripts\\assets_scripts_dialogs\\windialog.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|E:\\project\\DogBlast_new\\assets\\scripts\\assets_scripts_dialogs\\egodialog.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|solutionrelative:assets\\scripts\\assets_scripts_dialogs\\egodialog.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|E:\\project\\DogBlast_new\\assets\\resources\\language\\language.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}", "RelativeMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|solutionrelative:assets\\resources\\language\\language.xml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}"}, {"AbsoluteMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|E:\\project\\DogBlast_new\\assets\\scripts\\assets_scripts_dialogs\\lostdialog.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|solutionrelative:assets\\scripts\\assets_scripts_dialogs\\lostdialog.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|E:\\project\\DogBlast_new\\assets\\scripts\\_syl\\manager\\storemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|solutionrelative:assets\\scripts\\_syl\\manager\\storemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|E:\\project\\DogBlast_new\\assets\\scripts\\casualtools_dialogs\\mapchestbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}|Assembly-CSharp.csproj|solutionrelative:assets\\scripts\\casualtools_dialogs\\mapchestbutton.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "InventoryHelper.cs", "DocumentMoniker": "E:\\project\\DogBlast_new\\Assets\\Scripts\\Assets_Scripts_DataHelpers\\InventoryHelper.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\Assets_Scripts_DataHelpers\\InventoryHelper.cs", "ToolTip": "E:\\project\\DogBlast_new\\Assets\\Scripts\\Assets_Scripts_DataHelpers\\InventoryHelper.cs", "RelativeToolTip": "Assets\\Scripts\\Assets_Scripts_DataHelpers\\InventoryHelper.cs", "ViewState": "AgIAADIBAAAAAAAAAAAuwGYBAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T09:21:38.496Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "WinDialog.cs", "DocumentMoniker": "E:\\project\\DogBlast_new\\Assets\\Scripts\\Assets_Scripts_Dialogs\\WinDialog.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\Assets_Scripts_Dialogs\\WinDialog.cs", "ToolTip": "E:\\project\\DogBlast_new\\Assets\\Scripts\\Assets_Scripts_Dialogs\\WinDialog.cs", "RelativeToolTip": "Assets\\Scripts\\Assets_Scripts_Dialogs\\WinDialog.cs", "ViewState": "AgIAANsCAAAAAAAAAAAkwAMDAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-13T09:02:05.563Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 2, "Title": "GameVersionManager.cs", "DocumentMoniker": "E:\\project\\DogBlast_new\\Assets\\Scripts\\_SYL\\Common\\GameVersionManager.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\_SYL\\Common\\GameVersionManager.cs", "ToolTip": "E:\\project\\DogBlast_new\\Assets\\Scripts\\_SYL\\Common\\GameVersionManager.cs", "RelativeToolTip": "Assets\\Scripts\\_SYL\\Common\\GameVersionManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAwwCcAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T01:22:52.402Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "ActivityButton.cs", "DocumentMoniker": "E:\\project\\DogBlast_new\\Assets\\Scripts\\MapScene\\ActivityButton.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\MapScene\\ActivityButton.cs", "ToolTip": "E:\\project\\DogBlast_new\\Assets\\Scripts\\MapScene\\ActivityButton.cs", "RelativeToolTip": "Assets\\Scripts\\MapScene\\ActivityButton.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADMAAABhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T03:22:41.589Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CoinVideoDisplay.cs", "DocumentMoniker": "E:\\project\\DogBlast_new\\Assets\\Scripts\\MapScene\\CoinVideoDisplay.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\MapScene\\CoinVideoDisplay.cs", "ToolTip": "E:\\project\\DogBlast_new\\Assets\\Scripts\\MapScene\\CoinVideoDisplay.cs", "RelativeToolTip": "Assets\\Scripts\\MapScene\\CoinVideoDisplay.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADEAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T03:25:56.278Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "EgoDialog.cs", "DocumentMoniker": "E:\\project\\DogBlast_new\\Assets\\Scripts\\Assets_Scripts_Dialogs\\EgoDialog.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\Assets_Scripts_Dialogs\\EgoDialog.cs", "ToolTip": "E:\\project\\DogBlast_new\\Assets\\Scripts\\Assets_Scripts_Dialogs\\EgoDialog.cs", "RelativeToolTip": "Assets\\Scripts\\Assets_Scripts_Dialogs\\EgoDialog.cs", "ViewState": "AgIAAAoAAAAAAAAAAAAmwDIAAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T05:52:33.368Z"}, {"$type": "Document", "DocumentIndex": 6, "Title": "Language.xml", "DocumentMoniker": "E:\\project\\DogBlast_new\\Assets\\Resources\\language\\Language.xml", "RelativeDocumentMoniker": "Assets\\Resources\\language\\Language.xml", "ToolTip": "E:\\project\\DogBlast_new\\Assets\\Resources\\language\\Language.xml", "RelativeToolTip": "Assets\\Resources\\language\\Language.xml", "ViewState": "AgIAALEBAAAAAAAAAAAAAPkBAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003576|", "WhenOpened": "2025-06-19T03:39:48.512Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "LostDialog.cs", "DocumentMoniker": "E:\\project\\DogBlast_new\\Assets\\Scripts\\Assets_Scripts_Dialogs\\LostDialog.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\Assets_Scripts_Dialogs\\LostDialog.cs", "ToolTip": "E:\\project\\DogBlast_new\\Assets\\Scripts\\Assets_Scripts_Dialogs\\LostDialog.cs", "RelativeToolTip": "Assets\\Scripts\\Assets_Scripts_Dialogs\\LostDialog.cs", "ViewState": "AgIAADEAAAAAAAAAAAAkwF8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T03:32:06.765Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "MapChestButton.cs", "DocumentMoniker": "E:\\project\\DogBlast_new\\Assets\\Scripts\\CasualTools_Dialogs\\MapChestButton.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\CasualTools_Dialogs\\MapChestButton.cs", "ToolTip": "E:\\project\\DogBlast_new\\Assets\\Scripts\\CasualTools_Dialogs\\MapChestButton.cs", "RelativeToolTip": "Assets\\Scripts\\CasualTools_Dialogs\\MapChestButton.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T03:22:13.922Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "StoreManager.cs", "DocumentMoniker": "E:\\project\\DogBlast_new\\Assets\\Scripts\\_SYL\\Manager\\StoreManager.cs", "RelativeDocumentMoniker": "Assets\\Scripts\\_SYL\\Manager\\StoreManager.cs", "ToolTip": "E:\\project\\DogBlast_new\\Assets\\Scripts\\_SYL\\Manager\\StoreManager.cs", "RelativeToolTip": "Assets\\Scripts\\_SYL\\Manager\\StoreManager.cs", "ViewState": "AgIAAEcAAAAAAAAAAAAUwBkAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-19T02:51:54.528Z"}]}]}]}