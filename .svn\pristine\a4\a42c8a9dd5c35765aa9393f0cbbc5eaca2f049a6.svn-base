﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using NewMapScene;
using LGPOfflineSDK;

public class InitGameSet : MonoBehaviour
{

    public static InitGameSet Instance;

    [HowinInspectorLabel("背景图加载方式：true->png")]
    public bool beLoadSpriteByPng;

    [HowinInspect<PERSON><PERSON>abel("试玩关卡")]
    public int _quickLoadLevel;
    [HowinInspect<PERSON><PERSON>abel("试玩关卡")]
    public bool beTestPlayLevel;

    [HowinInspectorLabel("屏蔽评价界面")]
    public bool beHideRateUsDialog;

    [HowinInspect<PERSON><PERSON>abel("封面背景数量")]
    public int MapBackgroundImgNum;


    [HowinInspectorLabel("修改排行榜数据")]
    public bool beChangeRankData;


    [HowinInspectorLabel("小地图图片")]
    public bool beUseMapBG;

    [HowinInspectorLabel("测试IOS刘海屏")]
    public bool beTestIphoneLH;

    [HowinInspect<PERSON><PERSON>abel("测试兑换码")]
    public bool beTestCDkey;
    public string TestCDKey;


    [HowinInspectorLabel("测试计费")]
    public bool beTestPayment;


    [HowinInspectorLabel("测试banner")]
    public bool beTestBanner;
    
    public delegate void OnChangeShowBanner(bool show);

    public static OnChangeShowBanner OnChangeShowBannerEvent;

    public static bool beHasShowBanner;

    private void Awake()
    {
        Instance = this;
        DontDestroyOnLoad(gameObject);
        
    }

    //private void OnGUI()
    //{
    //    if (beTestPlayLevel)
    //    {
    //        _quickLoadLevel = int.Parse(GUILayout.TextField(_quickLoadLevel.ToString(), 120));
    //        if (GUILayout.Button("试玩关卡"))
    //        {
    //            QuickTestPlayLevel();
    //        }
    //    }

    //}

    public void QuickTestPlayLevel()
    {
        NewMapPage.Instance.CurrentMapManager.OnLevelStopClick(this._quickLoadLevel);
    }
   

    public float AdjustShowBanner(float initY)
    {
        if (LGPOfflineSDKAPI.HasBannerShow() || beHasShowBanner)
        {
            float w = LGPOfflineSDKAPI.GetBannerHeight();
            if (w <= 0)
            {
                w = 90;
            }
            float dis = w * 1.0f / 100;
            initY -= dis;
        }
        return initY;
    }
}
