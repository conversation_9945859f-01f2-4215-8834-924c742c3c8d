﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <LangVersion>7.3</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>10.0.20506</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <RootNamespace></RootNamespace>
    <ProjectGuid>{7B3AAB04-6D42-9655-1F10-60DBFAEBC5A7}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFrameworkVersion>v4.7.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <NoWarn>0169;USG0001</NoWarn>
    <DefineConstants>UNITY_2019_4_40;UNITY_2019_4;UNITY_2019;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_UNET;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_COLLAB;ENABLE_CLOUD_SERVICES_COLLAB_SOFTLOCKS;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_UNET;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;INCLUDE_DYNAMIC_GI;ENABLE_MONO_BDWGC;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;PLATFORM_STANDALONE;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;ENABLE_WEBSOCKET_HOST;ENABLE_MONO;NET_STANDARD_2_0;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;UNITY_PRO_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_LEGACY_INPUT_MANAGER;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <NoConfig>true</NoConfig>
    <NoStdLib>true</NoStdLib>
    <AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>
    <ImplicitlyExpandNETStandardFacades>false</ImplicitlyExpandNETStandardFacades>
    <ImplicitlyExpandDesignTimeFacades>false</ImplicitlyExpandDesignTimeFacades>
  </PropertyGroup>
  <PropertyGroup>
    <ProjectTypeGuids>{E097FAD1-6243-4DAD-9C02-E9B9EFC3FFC1};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.23</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>Legacy</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>2019.4.40f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="E:\ide\Microsoft Visual Studio\2022\Enterprise\Common7\IDE\Extensions\Microsoft\Visual Studio Tools for Unity\Analyzers\Microsoft.Unity.Analyzers.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\Scripts\AlphaMaterialModifier.cs" />
    <Compile Include="Assets\Scripts\AnvilBooster.cs" />
    <Compile Include="Assets\Scripts\ArrayMetadata.cs" />
    <Compile Include="Assets\Scripts\AssetBundleUtils.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend\BackendCaller.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend\BackendRequestHandler.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend\Command.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend\NetworkHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend\Reply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend\ReplyReason.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend\ResultCodesExtension.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\CommandManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\ConnectToFacebookCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\ConnectToFacebookReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\FbConnectAndLinkServices.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\GetFriendsScoresCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\GetFriendsScoresReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\LinkDeviceGenerateTicketCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\LinkDeviceGenerateTicketReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\LinkDeviceLoginCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\LinkDeviceLoginReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\LoginCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\LoginReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\PingCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\PingReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\SupportCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\SupportReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\SyncChestCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\SyncChestReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\SyncFullCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\SyncInventoryCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\SyncInventoryReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\SyncLevelCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\SyncLevelReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\UploadLogFileCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\UploadLogFileReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\ValidatePaymentCommand.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Backend_Commands\ValidatePaymentReply.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Billing\BundlePackage.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Billing\CaravanBillingHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Billing\SalesData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_DB\DataRow.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_DB\DataTable.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_DB\DatabaseManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_DB\Entity.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_DB\GenericDao.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_DB_impl\SqliteDatabase.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_DB_impl\SqliteException.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Logging\CompressComplete.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Logging\LogManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Logging\LogType.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Logging\LogUploader.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Logging\UploadComplete.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Logging_impl\LogFileWriter.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Logging_impl\SimpleAction.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Logging_impl\ThreadAdapter.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Logging_impl\ThreadedLogger.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Pooling\ObjectPool.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Pooling\ObjectPoolExtensions.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Tasks\LifeTime.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Tasks\NativeThreadHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Tasks\Task.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Tasks\TaskManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Common_Zip\SimpleZip.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Dialogs\BaseButton.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Dialogs\Dialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Dialogs\DialogManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Dialogs\EventBasedButton.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Dialogs\EventBasedSpriteButton.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Dialogs\EventBasedTouch.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Dialogs\NotScrollableSpriteButton.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Dialogs\SpriteButton.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_Dialogs\SpriteButtonListener.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_DownloadManager\DownloadInProgresData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_DownloadManager\DownloadManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_DownloadManager\ResumeDownloader.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_DownloadManager_DAO\DownloadMetaDataDao.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_DownloadManager_Entities\DownloadMetaData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_SimpleProperties_DAO\SimpleDbPropertyDao.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_CasualTools_SimpleProperties_Entities\SimpleDbProperty.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DAO\InboxDAO.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DAO\InventoryDao.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DAO\LevelDao.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DAO\OnceDAO.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DAO\SimpleSyncPropertiesDAO.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DAO_Entity\InventoryItemEntity.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DAO_Entity\LevelEntity.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DAO_Entity\OnceEntity.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DAO_Entity\SimpleSyncEntity.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\FastPropertiesHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\IReset.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\InventoryHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\InventoryItemType.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\LevelHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\LifeStatusHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\OnceDataHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\ResetManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\UserIdHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\UserSettings.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_DataHelpers\UserType.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\BoosterSelectDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\BoosterUnlockedDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\BuyResourcesDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\ChatScrollController.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\DailyBonusDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\DailyBonusItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\DiscountBundleDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\EgoDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\FacebookFriendItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\FacebookLogoutConfirmation.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\FacebookNotConnectedDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\FullscreenDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\GenericMessageDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\GoalsBannerDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\HighScoresPanel.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\HighscoreFriendDisplay.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\InviteFriendsDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\LinkDeviceDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\LinkDeviceEnterPinDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\LinkDeviceResultDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\LinkDeviceShowPinDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\LostDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\OutOfLivesDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\PaintBrushDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\PrelevelDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\PrelevelDialogBooster.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\PurchaseSuccessDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\QuitDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\RateUsDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\ScrollController.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\SelectFriendDisplay.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\SimpleDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\StarChestDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\StarSlider.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\SupportDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\TeamChestDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\ToonChestDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Dialogs\WinDialog.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Extensions\DateTimeExtensions.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Extensions\DoTweenExtensions.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Extensions\GameObjectExtensions.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Extensions\GridExtensions.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Extensions\IntExtensions.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Extensions\ListExtensions.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Extensions\Statistics.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Extensions\TiledToGroupId.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Extensions\TiledToItemType.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\BalloonParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\BurstModifierParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\ColoredBalloonParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\ColoredCrateExplodeParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\ColoredCrateLayerRemovedParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\DiscoExplosionParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\DiscoRayParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\EasterEggExplosionParticle.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\EasterEggTransformationParticle.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\ParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\ParticlePool.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\ScoreManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\ShadowParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\SolidColorParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\SortingParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\TimeScaleScroller.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene\VaseTransformParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\BoosterManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\BorderBuilder.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\BoxingGloveBooster.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\CaravanGrid.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\Cell.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\CellData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\DefinedSorting.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\DefinedSortingsExtensions.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\Direction.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\ExplodeReason.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\FallManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\Goal.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\GroupId.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\GroupIdComparer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\HammerBooster2D.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\HintManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\IExplodeAware.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\ItemGroup.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\ItemProperties.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\ItemType.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\JellyManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\Level.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\LevelBuilder.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\MatchFinder.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\MatchGroup.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\MatchType.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\MatchTypeComparer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\ShuffleManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics\TiledEditorId.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\BalloonSquashAndStretchOnFall.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\BirdCollectStrategy.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\BounceOnFall.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\CarrotCollectStrategy.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\CollectAnimation.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\CollectManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\DefaultCollectStrategy.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\DuckCollectStrategy.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\FallAnimation.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\FallListener.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\GiantPinataAnimationController.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\HoneyItemAnimationController.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\ICollectStrategy.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\MoveToSpecialItemAnimation.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\OysterItemAnimationController.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\ShakeAnimation.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\ShuffleAnimation.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\SolidColorCollectStrategy.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Animations\SquashAndStrechOnFall.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_GroupConditions\CountOfCondition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_GroupConditions\GroupCondition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\CageItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\CanBox.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\CanTossFakeItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\CanTossItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\ColoredCrateItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\CrateItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\DuckItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\EasterEggItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\GiantDuckFakeItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\GiantDuckItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\GiantPinataItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\GiantPinateFakeItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\ICanExplodeAtLeast2.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\Item.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\LightBulbItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\MagicHatItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\OysterItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\SolidColorItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\SpriteBasedItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items\WatermelonItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items_ComboItems\IComboItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items_Features\CanCastShadowComponent.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items_Features\ICanBePaint.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items_Features\ICanShowCurrentlyUnderTap.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Mechanics_Items_SpecialItems\SpecialItem.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Touches\AbstractTapListener.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Touches\BoosterTapListener.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Touches\TapListener.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\HighlightCellsTutorial.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\HighlightItemsTutorial.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\MapTutorial.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\PrelevelTutorial.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial1.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial10.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial1001.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial101.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial1101.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial1201.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial121.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial13.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial1301.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial1401.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial141.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial15.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial1501.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial1601.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial161.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial17.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial1701.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial181.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial2.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial20.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial201.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial21.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial241.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial281.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial3.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial31.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial321.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial351.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial4.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial401.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial41.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial451.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial5.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial501.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial51.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial551.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial6.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial601.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial61.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial651.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial701.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial71.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial751.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial801.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial81.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial851.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial9.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial901.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\Tutorial951.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\TutorialBase.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\TutorialCellDisplayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_Tutorials\TutorialManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_UI\CaravanTopPanel.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_UI\ScoreBarStar.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_UI\SettingsPanel.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_GamePlayScene_UI\TopPanelGoalUI.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_LevelLoaderScene\LevelScreenShotTaker.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Logging\CaravanBestHttpLogger.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Logging\LogTags.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_MapScene\FacebookCircularImageProcessor.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_MapScene\InboxTab.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_MapScene\LevelStar.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_MapScene\MapLivesDisplay.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_MapScene\MapManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB\PeakAB.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Conditions\BreakCondition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Conditions\FacebookCondition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Conditions\LevelCondition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Conditions\PlatformCondition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Conditions\ProcessAtLevelCondition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Conditions\VersionCondition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Conditions_bases\ICondition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Conditions_bases\SimpleIntegerCondition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_LocalTests\ILocalAB.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Pocos\ABTest.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Pocos\Condition.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Pocos\Result.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_Pocos\Variant.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_VariantProcessors\BaseVariantProcessor.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_VariantProcessors\CoinRewardProcessor.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_VariantProcessors\EgoProcessor.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_VariantProcessors\LevelChestProcessor.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_VariantProcessors\LevelProcessor.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_VariantProcessors\LifeDurationProcessor.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_VariantProcessors\PiggyBankProcessor.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_PeakAB_VariantProcessors\StartCoinsProcessor.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_SceneTransitions\CameraSizer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_SceneTransitions\CaravanSceneManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_SceneTransitions\GamePlayCameraSizer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_SceneTransitions\LoadingScreenDisplayer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_SceneTransitions\LoadingScreenResizer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_SceneTransitions\LoadingType.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_SceneTransitions\Photographer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_SceneTransitions\Scenes.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\AudioLibrary.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\AudioManager.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\AudioTag.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\Auto.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\AutoEase.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\AutoEaseType.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\BlurFollower.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\CameraHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\Constants.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\DownloadPurposes.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\DynamicScaler.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\Easer.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\FacebookDialogRequest.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\FacebookFriendsHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\FacebookImageCache.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\FacebookImageUtils.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\FastLocalize.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\FastLocalizeEx.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\FocusListener.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\HelpshiftCallbacks.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\IDestroyedAware.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\LoginUtils.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\PlayMode.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\PlayRandomSpineAnimations.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\Predicate.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\SimpleLimitedCache.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\SlowCheckTime.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\Stingers.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\StringExtensions.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\SupportHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils\VersionUpgrade.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\AdjustHelper.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\AnalyticsTags.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonBaseData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonBuyCoinsData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonChangeName.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonCpu.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonCpuData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonDailyBonus.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonDailyBonusGifts.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonDeviceData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonDeviceDataWithInventory.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonEventData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonFacebookConnectData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonFacebookDisconnectData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonInventory.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonLevelChest.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonLevelEndData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonLevelEndInventory.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonLevelStarChest.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonLifeAsk.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonLifeHack.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonLifeHelp.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonPingContent.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonPingData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonPurchaseData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonSessionData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonSessionDataWithInventory.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonSocialInventoryHC.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonSocialNickname.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonSpendData.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonStarChestGifts.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonStartLevel.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonTeamChest.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonTeamChestGifts.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonTeamCreate.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonTeamEdit.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonTeamJoin.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonTeamLeave.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonTeamTournament.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonTimeout.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\JsonToonChestGifts.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_Analytics\PeakAnalytics.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_NativeTools\CaravanNativeTools.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_NativeTools\ICaravanNativeTools.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_NativeTools_impls\AndroidCaravanNativeTools.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_NativeTools_impls\AndroidCaravanNativeToolsCallbacks.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_NativeTools_impls\EditorCaravanNativeTools.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_NativeTools_impls\NativeToolsSupport.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_SortingLayer\SortingLayerAttribute.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_SortingLayer\SortingLayerExposed.cs" />
    <Compile Include="Assets\Scripts\Assets_Scripts_Utils_TextCurver\TextCurver.cs" />
    <Compile Include="Assets\Scripts\AudioRequest.cs" />
    <Compile Include="Assets\Scripts\Backend\RemoteConfigChecker.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\AutoLoginIdCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\AutoLoginIdReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLClaimCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLClaimReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLGetLeaderboardCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLGetLeaderboardReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLGetStatusCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLJoinCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLJoinReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLSyncLevelCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLSyncLevelReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLUserScoresCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CLUserScoresReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\ChangeNicknameCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\ChangeNicknameReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\ConsentAddCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\ConsentAddReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CreateSocialUserCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\CreateSocialUserReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\DailyClaimCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\DailyClaimReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetAllLeaderboardsCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetAllLeaderboardsReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetEventsCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetEventsReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetFacebookLeaderboardCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetFacebookLeaderboardReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetInfoCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetInfoReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetPlayersLeaderboardCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetPlayersLeaderboardReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetTeamInfoCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetTeamInfoReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetTeamLeaderboardCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\GetTeamLeaderboardReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\StClaimCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\StClaimReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\StGetStatusCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\StGetStatusReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\SyncFullReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\TcClaimCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\TcClaimReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\TcGetStatusCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\TcGetStatusReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\TeamChangeCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\TeamChangeReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\TtClaimCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\TtClaimReply.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\TtGetStatusCommand.cs" />
    <Compile Include="Assets\Scripts\Backend_Commands\TtGetStatusReply.cs" />
    <Compile Include="Assets\Scripts\BalloonGeneratorAnimationEvents.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\ConnectionBase.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\FileConnection.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPConnection.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPConnectionRecycledDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPConnectionStates.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPManager.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPMethods.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPProtocolFactory.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPProxy.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPRange.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPRequest.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPRequestStates.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPResponse.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\HTTPUpdateDelegator.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\IProtocol.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\KeepAliveHeader.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\OnBeforeHeaderSendDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\OnBeforeRedirectionDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\OnDownloadProgressDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\OnHeaderEnumerationDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\OnRequestFinishedDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\OnUploadProgressDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\RetryCauses.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\StreamList.cs" />
    <Compile Include="Assets\Scripts\BestHTTP\SupportedProtocols.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Authentication\AuthenticationTypes.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Authentication\Credentials.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Authentication\Digest.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Authentication\DigestStore.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Caching\HTTPCacheFileInfo.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Caching\HTTPCacheFileLock.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Caching\HTTPCacheMaintananceParams.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Caching\HTTPCacheService.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Caching\UriComparer.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Cookies\Cookie.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Cookies\CookieJar.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Crc\CRC32.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\Adler.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\BlockState.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\CompressionLevel.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\CompressionMode.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\CompressionStrategy.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\DeflateFlavor.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\DeflateManager.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\DeflateStream.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\FlushType.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\GZipStream.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\InfTree.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\InflateBlocks.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\InflateCodes.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\InflateManager.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\InternalConstants.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\InternalInflateConstants.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\SharedUtils.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\StaticTree.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\ZTree.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\ZlibBaseStream.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\ZlibCodec.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\ZlibConstants.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\ZlibException.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Decompression_Zlib\ZlibStreamFlavor.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Extensions\ExceptionHelper.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Extensions\Extensions.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Extensions\HeaderParser.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Extensions\HeaderValue.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Extensions\HeartbeatManager.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Extensions\IHeartbeat.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Extensions\KeyValuePairList.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Extensions\WWWAuthenticateHeaderParser.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Forms\HTTPFieldData.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Forms\HTTPFormBase.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Forms\HTTPFormUsage.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Forms\HTTPMultiPartForm.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Forms\HTTPUrlEncodedForm.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Forms\RawJsonForm.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Forms\UnityForm.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_JSON\Json.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Logger\DefaultLogger.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Logger\ILogger.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Logger\Loglevels.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_PlatformSupport_TcpClient_General\TcpClient.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_ServerSentEvents\EventSource.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_ServerSentEvents\EventSourceResponse.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_ServerSentEvents\Message.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_ServerSentEvents\OnErrorDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_ServerSentEvents\OnEventDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_ServerSentEvents\OnGeneralEventDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_ServerSentEvents\OnMessageDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_ServerSentEvents\OnRetryDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_ServerSentEvents\OnStateChangedDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_ServerSentEvents\States.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\Connection.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\ConnectionStates.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\IConnection.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\MessageTypes.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\NegotiationData.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\OnClosedDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\OnConnectedDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\OnErrorDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\OnNonHubMessageDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\OnPrepareRequestDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\OnStateChanged.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\ProtocolVersions.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\RequestTypes.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\TransportStates.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR\TransportTypes.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Authentication\IAuthenticationProvider.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Authentication\OnAuthenticationFailedDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Authentication\OnAuthenticationSuccededDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Hubs\Hub.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Hubs\IHub.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Hubs\OnMethodCallCallbackDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Hubs\OnMethodCallDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Hubs\OnMethodFailedDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Hubs\OnMethodProgressDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Hubs\OnMethodResultDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_JsonEncoders\DefaultJsonEncoder.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_JsonEncoders\IJsonEncoder.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Messages\DataMessage.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Messages\FailureMessage.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Messages\IHubMessage.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Messages\IServerMessage.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Messages\KeepAliveMessage.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Messages\MethodCallMessage.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Messages\MultiMessage.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Messages\ProgressMessage.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Messages\ResultMessage.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Transports\OnTransportStateChangedDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Transports\PollingTransport.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Transports\PostSendTransportBase.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Transports\ServerSentEventsTransport.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Transports\TransportBase.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SignalR_Transports\WebSocketTransport.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\Error.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\HandshakeData.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\IManager.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\ISocket.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\Packet.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\Socket.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\SocketIOErrors.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\SocketIOEventTypes.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\SocketManager.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\SocketOptions.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO\TransportEventTypes.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_Events\EventDescriptor.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_Events\EventNames.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_Events\EventTable.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_Events\SocketIOAckCallback.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_Events\SocketIOCallback.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_JsonEncoders\DefaultJSonEncoder.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_JsonEncoders\IJsonEncoder.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_JsonEncoders\LitJsonEncoder.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_Transports\ITransport.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_Transports\PollingTransport.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_Transports\TransportStates.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_Transports\TransportTypes.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_SocketIO_Transports\WebSocketTransport.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_Statistics\StatisticsQueryFlags.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket\OnWebSocketBinaryDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket\OnWebSocketClosedDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket\OnWebSocketErrorDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket\OnWebSocketErrorDescriptionDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket\OnWebSocketIncompleteFrameDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket\OnWebSocketMessageDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket\OnWebSocketOpenDelegate.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket\WebSocket.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket\WebSocketResponse.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket\WebSocketStausCodes.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket_Extensions\IExtension.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket_Extensions\PerMessageCompression.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket_Frames\WebSocketFrame.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket_Frames\WebSocketFrameReader.cs" />
    <Compile Include="Assets\Scripts\BestHTTP_WebSocket_Frames\WebSocketFrameTypes.cs" />
    <Compile Include="Assets\Scripts\BezierCurve.cs" />
    <Compile Include="Assets\Scripts\BezierPoint.cs" />
    <Compile Include="Assets\Scripts\BlasterAnimationScript.cs" />
    <Compile Include="Assets\Scripts\BlasterCollectAnimation.cs" />
    <Compile Include="Assets\Scripts\BlasterCollectData.cs" />
    <Compile Include="Assets\Scripts\BlasterCollectManager.cs" />
    <Compile Include="Assets\Scripts\BoosterTutorial.cs" />
    <Compile Include="Assets\Scripts\BoosterUseBackgroundPanel.cs" />
    <Compile Include="Assets\Scripts\BubbleAnimation.cs" />
    <Compile Include="Assets\Scripts\CLGetStatusReply.cs" />
    <Compile Include="Assets\Scripts\CLLeaderboardItemData.cs" />
    <Compile Include="Assets\Scripts\CachedEvent.cs" />
    <Compile Include="Assets\Scripts\CaretInfo.cs" />
    <Compile Include="Assets\Scripts\CasualTools_Dialogs\MapChestButton.cs" />
    <Compile Include="Assets\Scripts\CasualTools_Dialogs\ToonChestDisplay.cs" />
    <Compile Include="Assets\Scripts\CasualTools_Dialogs\TouchBounds.cs" />
    <Compile Include="Assets\Scripts\CasualTools_Dialogs\TouchBoundsListener.cs" />
    <Compile Include="Assets\Scripts\CellPairs.cs" />
    <Compile Include="Assets\Scripts\ChatScrollTest.cs" />
    <Compile Include="Assets\Scripts\ClLevelInfo.cs" />
    <Compile Include="Assets\Scripts\ClearStencilBufferComponent.cs" />
    <Compile Include="Assets\Scripts\ClientMessage.cs" />
    <Compile Include="Assets\Scripts\CoinData.cs" />
    <Compile Include="Assets\Scripts\CollectData.cs" />
    <Compile Include="Assets\Scripts\ConsoleProDebug.cs" />
    <Compile Include="Assets\Scripts\CreateTeamLevelSlider.cs" />
    <Compile Include="Assets\Scripts\CreateTeamTypeSlider.cs" />
    <Compile Include="Assets\Scripts\CurveProps.cs" />
    <Compile Include="Assets\Scripts\DAO\EventDAO.cs" />
    <Compile Include="Assets\Scripts\DAO\FacebookLeaderboardDAO.cs" />
    <Compile Include="Assets\Scripts\DAO\PlayersLeaderboardDAO.cs" />
    <Compile Include="Assets\Scripts\DAO\TeamChestDao.cs" />
    <Compile Include="Assets\Scripts\DAO\TeamLeaderboardDAO.cs" />
    <Compile Include="Assets\Scripts\DAO_Entity\EventEntity.cs" />
    <Compile Include="Assets\Scripts\DAO_Entity\InboxEntity.cs" />
    <Compile Include="Assets\Scripts\DAO_Entity\LeaderboardItemEntity.cs" />
    <Compile Include="Assets\Scripts\DAO_Entity\PlayersLeaderboardEntity.cs" />
    <Compile Include="Assets\Scripts\DAO_Entity\TeamChestEntity.cs" />
    <Compile Include="Assets\Scripts\DAO_Entity\TeamLeaderboardItemEntity.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\AbDataHelper.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\CLRewardItem.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\CLRewardsJson.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\ChampionsLeagueHelper.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\ChestHelper.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\DailyBonusHelper.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\EventDataHelper.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\EventEntityList.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\ITimer.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\ServerInfoDataHelper.cs" />
    <Compile Include="Assets\Scripts\DataHelpers\TimerHelper.cs" />
    <Compile Include="Assets\Scripts\DefaultDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\CLEnterenceDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\CLLeaderboardDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\CLLeaderboardEntry.cs" />
    <Compile Include="Assets\Scripts\Dialogs\CLPrelevelDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\CLRewardsDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\CLRewardsEntry.cs" />
    <Compile Include="Assets\Scripts\Dialogs\ChestInfoDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\CloseBlockedDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\CrownRushInfoDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\CrownRushInfoDialogStage.cs" />
    <Compile Include="Assets\Scripts\Dialogs\EpisodeNavigationData.cs" />
    <Compile Include="Assets\Scripts\Dialogs\EpisodeNavigationDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\EpisodeNavigationItem.cs" />
    <Compile Include="Assets\Scripts\Dialogs\EpisodeUnlockedDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\EventTitleController.cs" />
    <Compile Include="Assets\Scripts\Dialogs\FacebookConnectedDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\KickButton.cs" />
    <Compile Include="Assets\Scripts\Dialogs\NewLevelsDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\OnCloseCallbackDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\ResultDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\RewardText.cs" />
    <Compile Include="Assets\Scripts\Dialogs\SettingsDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\StarTournamentDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\StarTournamentEntry.cs" />
    <Compile Include="Assets\Scripts\Dialogs\StarTournamentInfoDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\StarTournamentResultDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\StarTournamentRewardsDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\TeamChestInfoDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\TeamChestJoinTeamDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\TeamEditDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\TeamInfoDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\TeamInfoJoinDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\TeamTournamentAnnouncementDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\TeamTournamentInfoDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\TeamTournamentPlayerEntry.cs" />
    <Compile Include="Assets\Scripts\Dialogs\TeamTournamentRewardsDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs\TeamTournamentTeamEntry.cs" />
    <Compile Include="Assets\Scripts\Dialogs_BuyCoins\BundleBoosterItem.cs" />
    <Compile Include="Assets\Scripts\Dialogs_BuyCoins\BundleContent.cs" />
    <Compile Include="Assets\Scripts\Dialogs_BuyCoins\BundleEntry.cs" />
    <Compile Include="Assets\Scripts\Dialogs_BuyCoins\BundleShopDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs_BuyCoins\BundleShopPage.cs" />
    <Compile Include="Assets\Scripts\Dialogs_BuyCoins\BuyCoinsDialog.cs" />
    <Compile Include="Assets\Scripts\Dialogs_BuyCoins\CoinsEntry.cs" />
    <Compile Include="Assets\Scripts\DiscoRayRotate.cs" />
    <Compile Include="Assets\Scripts\DoubleDate.cs" />
    <Compile Include="Assets\Scripts\EasterEggTransformationWaiter.cs" />
    <Compile Include="Assets\Scripts\EventsManagers\AbstractEventManager.cs" />
    <Compile Include="Assets\Scripts\EventsManagers\CrownRushEventManager.cs" />
    <Compile Include="Assets\Scripts\EventsManagers\GeneralEventManager.cs" />
    <Compile Include="Assets\Scripts\EventsManagers\StRewardItem.cs" />
    <Compile Include="Assets\Scripts\EventsManagers\StRewardsJson.cs" />
    <Compile Include="Assets\Scripts\EventsManagers\StarTournamentEventManager.cs" />
    <Compile Include="Assets\Scripts\EventsManagers\TeamChestEventManager.cs" />
    <Compile Include="Assets\Scripts\EventsManagers\TeamTournamentEventManager.cs" />
    <Compile Include="Assets\Scripts\EventsManagers\TtConfigJson.cs" />
    <Compile Include="Assets\Scripts\Extents.cs" />
    <Compile Include="Assets\Scripts\FPSMeter.cs" />
    <Compile Include="Assets\Scripts\Facebook\FacebookEvents.cs" />
    <Compile Include="Assets\Scripts\FacebookEditorUtils.cs" />
    <Compile Include="Assets\Scripts\FacebookFriend.cs" />
    <Compile Include="Assets\Scripts\FlyingWormConsole3\ConsoleProRemoteServer.cs" />
    <Compile Include="Assets\Scripts\FontCreationSetting.cs" />
    <Compile Include="Assets\Scripts\GPGSIds.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene\PotionParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene\SnowParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene\SodaBottleParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene\SodaParticlePlayer.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics\BubbleController.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics\CollectorItems.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics\CollectorItemsController.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics\FireworksController.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics\ITouchAware.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics\ItemGeneratorController.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics\ItemResources.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics\ItemResourcesManager.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics\UfoController.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Animations\BalloonGeneratorThrowAnimation.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Animations\SwapAnimation.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Animations_Collections\PenguinCollectStrategy.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Animations_Collections\UfoCollectStrategy.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Animations_Collections\WinAnimation.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Animations_Falls\GiftSquashAndStretchOnFall.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\BalloonGeneratorItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\BalloonItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\BarrelItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\BillboardItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\BirdHouseItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\BlasterFakeItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\BlasterItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\CoconutItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\CollectorItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\ColoredBalloonItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\ColoredBalloonType.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\ColoredSodaFakeItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\ColoredSodaItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\DiamondItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\FireworksItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\FireworksRocket.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\FishItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\GeneratorBasedItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\GiftItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\HanoiItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\HoneyItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\IvyItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\JellyAnimatorWaiter.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\JellyItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\LayeredItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\MetalCrateItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\MoleItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\PenguinItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\PotionItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\SodaBottle.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\SodaFakeItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\SodaItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\StoneItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\StoneItemOder.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\UfoItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\VaseColorType.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\VaseItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items\WallItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items_ComboItems\DiscoBallAndBombItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items_ComboItems\DiscoBallAndRocketItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items_ComboItems\DoubleBombItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items_ComboItems\DoubleDiscoBallItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items_ComboItems\DoubleRocketItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items_ComboItems\RocketAndBombItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items_SpecialItems\BombItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items_SpecialItems\DiscoBallItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items_SpecialItems\HorizontalRocketItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Mechanics_Items_SpecialItems\VerticalRocketItem.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_Tutorials\HighlightItemTypeTutorial.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_UI\BlackPanel.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_UI\WinCharsAnimation.cs" />
    <Compile Include="Assets\Scripts\GamePlayScene_UI\WinLogoAnimation.cs" />
    <Compile Include="Assets\Scripts\GeneralStatistics.cs" />
    <Compile Include="Assets\Scripts\HSMiniJSON\Json.cs" />
    <Compile Include="Assets\Scripts\Helpshift\APICallInfo.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftAndroid.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftAndroidCampaignsDelegate.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftAndroidInboxDelegate.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftAndroidInboxMessage.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftAndroidInboxPushNotificationDelegate.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftAndroidLog.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftCampaignsAndroid.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftDexLoader.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftInbox.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftInboxAndroid.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftInboxMessage.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftInboxMessageActionType.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftInternalLogger.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftLog.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftSdk.cs" />
    <Compile Include="Assets\Scripts\Helpshift\HelpshiftWorker.cs" />
    <Compile Include="Assets\Scripts\Helpshift\IDexLoaderListener.cs" />
    <Compile Include="Assets\Scripts\Helpshift\IHelpshiftCampaignsDelegate.cs" />
    <Compile Include="Assets\Scripts\Helpshift\IHelpshiftInboxDelegate.cs" />
    <Compile Include="Assets\Scripts\Helpshift\IHelpshiftInboxPushNotificationDelegate.cs" />
    <Compile Include="Assets\Scripts\Helpshift\IWorkerMethodDispatcher.cs" />
    <Compile Include="Assets\Scripts\HelpshiftConfig.cs" />
    <Compile Include="Assets\Scripts\Helpshift_Campaigns\HelpshiftCampaigns.cs" />
    <Compile Include="Assets\Scripts\HeurekaGames\Singleton`1.cs" />
    <Compile Include="Assets\Scripts\HeurekaGames_AssetHunter\AssetHunterExtensions.cs" />
    <Compile Include="Assets\Scripts\HighlightMixedTutorial.cs" />
    <Compile Include="Assets\Scripts\HueColor.cs" />
    <Compile Include="Assets\Scripts\I2\CoroutineManager.cs" />
    <Compile Include="Assets\Scripts\I2\RenameAttribute.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\ArabicMapping.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\ArabicTable.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\AutoChangeCultureInfo.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\CoroutineManager.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\EventCallback.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\GeneralArabicLetters.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\GoogleLanguages.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\GoogleTranslation.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\HindiFixer.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\I2RuntimeInitialize.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\I2Utils.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\ILocalizationParamsManager.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\ILocalizeTarget.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\IResourceManager_Bundles.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\IsolatedArabicLetters.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LanguageData.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LanguageSource.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizationManager.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizationParamsManager.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizationReader.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\Localize.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeDropdown.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_TextMeshPro_TMPLabel.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_TextMeshPro_UGUI.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_UnityStd_AudioSource.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_UnityStd_Child.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_UnityStd_GUIText.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_UnityStd_Prefab.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_UnityStd_SpriteRenderer.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_UnityStd_TextMesh.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_UnityStd_Texture.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_UnityUI_Image.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_UnityUI_RawImage.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget_UnityUI_Text.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\LocalizeTarget`1.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\PersistentStorage.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\RTLFixer.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\RTLFixerTool.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\RegisterGlobalParameters.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\ResourceManager.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\ScriptLocalization.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\SetLanguage.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\SetLanguageDropdown.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\StringObfucator.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\TashkeelLocation.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\TermData.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\TermsPopup.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\TranslationFlag.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\eLanguageDataFlags.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\ePluralType.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\eSpreadsheetUpdateMode.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\eTermType.cs" />
    <Compile Include="Assets\Scripts\I2_Loc\eTransTag_Input.cs" />
    <Compile Include="Assets\Scripts\I2_Loc_SimpleJSON\JSON.cs" />
    <Compile Include="Assets\Scripts\I2_Loc_SimpleJSON\JSONArray.cs" />
    <Compile Include="Assets\Scripts\I2_Loc_SimpleJSON\JSONBinaryTag.cs" />
    <Compile Include="Assets\Scripts\I2_Loc_SimpleJSON\JSONClass.cs" />
    <Compile Include="Assets\Scripts\I2_Loc_SimpleJSON\JSONData.cs" />
    <Compile Include="Assets\Scripts\I2_Loc_SimpleJSON\JSONLazyCreator.cs" />
    <Compile Include="Assets\Scripts\I2_Loc_SimpleJSON\JSONNode.cs" />
    <Compile Include="Assets\Scripts\IlifeDialog.cs" />
    <Compile Include="Assets\Scripts\InitialScene\InitialSceneController.cs" />
    <Compile Include="Assets\Scripts\InitialScene\LoadingLogo.cs" />
    <Compile Include="Assets\Scripts\InitialScene\StartSceneController.cs" />
    <Compile Include="Assets\Scripts\InventoryItemTypeComparer.cs" />
    <Compile Include="Assets\Scripts\ItemDescription.cs" />
    <Compile Include="Assets\Scripts\ItemTypeComparer.cs" />
    <Compile Include="Assets\Scripts\JapaneseSunController.cs" />
    <Compile Include="Assets\Scripts\KerningPairKey.cs" />
    <Compile Include="Assets\Scripts\LastLevel.cs" />
    <Compile Include="Assets\Scripts\LevelLoaderScene\LevelLoaderController.cs" />
    <Compile Include="Assets\Scripts\LifeData.cs" />
    <Compile Include="Assets\Scripts\Line.cs" />
    <Compile Include="Assets\Scripts\LitJson\Condition.cs" />
    <Compile Include="Assets\Scripts\LitJson\ExporterFunc.cs" />
    <Compile Include="Assets\Scripts\LitJson\ExporterFunc`1.cs" />
    <Compile Include="Assets\Scripts\LitJson\FsmContext.cs" />
    <Compile Include="Assets\Scripts\LitJson\IJsonWrapper.cs" />
    <Compile Include="Assets\Scripts\LitJson\IOrderedDictionary.cs" />
    <Compile Include="Assets\Scripts\LitJson\ImporterFunc.cs" />
    <Compile Include="Assets\Scripts\LitJson\ImporterFunc_TJson_ TValue_.cs" />
    <Compile Include="Assets\Scripts\LitJson\JsonData.cs" />
    <Compile Include="Assets\Scripts\LitJson\JsonException.cs" />
    <Compile Include="Assets\Scripts\LitJson\JsonMapper.cs" />
    <Compile Include="Assets\Scripts\LitJson\JsonMockWrapper.cs" />
    <Compile Include="Assets\Scripts\LitJson\JsonReader.cs" />
    <Compile Include="Assets\Scripts\LitJson\JsonToken.cs" />
    <Compile Include="Assets\Scripts\LitJson\JsonType.cs" />
    <Compile Include="Assets\Scripts\LitJson\JsonWriter.cs" />
    <Compile Include="Assets\Scripts\LitJson\Lexer.cs" />
    <Compile Include="Assets\Scripts\LitJson\OrderedDictionaryEnumerator.cs" />
    <Compile Include="Assets\Scripts\LitJson\ParserToken.cs" />
    <Compile Include="Assets\Scripts\LitJson\WrapperFactory.cs" />
    <Compile Include="Assets\Scripts\LitJson\WriterContext.cs" />
    <Compile Include="Assets\Scripts\LivesInbox.cs" />
    <Compile Include="Assets\Scripts\LoadingInfoDialog.cs" />
    <Compile Include="Assets\Scripts\LocalizedString.cs" />
    <Compile Include="Assets\Scripts\MapScene\ActivityButton.cs" />
    <Compile Include="Assets\Scripts\MapScene\CLRankDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\CLStageButton.cs" />
    <Compile Include="Assets\Scripts\MapScene\CoinVideoDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\CrownDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\CrownRushDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\DailyBonusDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\FadingAndFloatingText.cs" />
    <Compile Include="Assets\Scripts\MapScene\LevelButton.cs" />
    <Compile Include="Assets\Scripts\MapScene\LevelButtonAnimationController.cs" />
    <Compile Include="Assets\Scripts\MapScene\MapChestDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\MapCoinsDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\MapDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\MapDisplayController.cs" />
    <Compile Include="Assets\Scripts\MapScene\MapSettingsDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\MapSide.cs" />
    <Compile Include="Assets\Scripts\MapScene\MegaBundleDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\StarChestDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\StarTournamentDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\TeamChestDisplay.cs" />
    <Compile Include="Assets\Scripts\MapScene\TeamTournamentAnimationController.cs" />
    <Compile Include="Assets\Scripts\MapScene\TeamTournamentDisplay.cs" />
    <Compile Include="Assets\Scripts\MaterialReference.cs" />
    <Compile Include="Assets\Scripts\MaterialReference1.cs" />
    <Compile Include="Assets\Scripts\Medvedya_GeometryMath\Line3d.cs" />
    <Compile Include="Assets\Scripts\Medvedya_GeometryMath\Polygon.cs" />
    <Compile Include="Assets\Scripts\Medvedya_GeometryMath\Vector2Utillites.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\Edge.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\EdgeDivider.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\EdgeSerialization.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\MainToolBarInspector.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\PointConstrain.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\SpriteDeformer.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\SpriteDeformerAnimation.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\SpriteDeformerBlendShape.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\SpriteDeformerBlendShapeAnimatorProxy.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\SpriteDeformerEditorSaver.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\SpriteDeformerStatic.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\SpriteDeformerTargetPoints.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\SpriteDeformerWithMaterialPropertyBlock.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\SpritePoint.cs" />
    <Compile Include="Assets\Scripts\Medvedya_SpriteDeformerTools\Triangulator.cs" />
    <Compile Include="Assets\Scripts\Mesh_Extents.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\AskLifeNotificationIcon.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\ChatHelpNotificationIcon.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\CollectCoin.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\ContainerManager.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\FacebookLeaderboardTabPage.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\LeaderboardPageController.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\LivesPageController.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\MapAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\MapPage.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\MapPageSizer.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\MapUIAnimationController.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\NewMapAnimationController.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\NewMapPage.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\PlayersLeaderboardTabPage.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\ShopPage.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\SwipeManager.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\TabButton.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\TabPage.cs" />
    <Compile Include="Assets\Scripts\NewMapScene\TeamLeaderboardTabPage.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\BoosterUnlockAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\ChampionsLeagueAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\ConsentPopupAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\CrownRushAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\DailyBonusTutorialAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\EpisodeUnlockedAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\FacebookStatusAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\InitialDialogsAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\LevelUnlockedAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\LifeHackAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\MoreLevelsDialogAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\PurchaseRetrySuccessAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\RateUsDialogAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\SocialTutorialAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\StarChestInitialOpenAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\StarTournamentAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\StaticMapStarCollectAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\TeamChestAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\TeamTournamentAnimation.cs" />
    <Compile Include="Assets\Scripts\NewMapScene_MapAnimations\ToonChestAnimation.cs" />
    <Compile Include="Assets\Scripts\OSNotification.cs" />
    <Compile Include="Assets\Scripts\OSNotificationAction.cs" />
    <Compile Include="Assets\Scripts\OSNotificationOpenedResult.cs" />
    <Compile Include="Assets\Scripts\OSNotificationPayload.cs" />
    <Compile Include="Assets\Scripts\OSNotificationPermission.cs" />
    <Compile Include="Assets\Scripts\OSPermissionState.cs" />
    <Compile Include="Assets\Scripts\OSPermissionStateChanges.cs" />
    <Compile Include="Assets\Scripts\OSPermissionSubscriptionState.cs" />
    <Compile Include="Assets\Scripts\OSSubscriptionState.cs" />
    <Compile Include="Assets\Scripts\OSSubscriptionStateChanges.cs" />
    <Compile Include="Assets\Scripts\ObjectMetadata.cs" />
    <Compile Include="Assets\Scripts\OneSignal.cs" />
    <Compile Include="Assets\Scripts\OneSignalAndroid.cs" />
    <Compile Include="Assets\Scripts\OneSignalPlatform.cs" />
    <Compile Include="Assets\Scripts\OneSignalPlatformHelper.cs" />
    <Compile Include="Assets\Scripts\OneSignalPush_MiniJSON\Json.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1Encodable.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1EncodableVector.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1Exception.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1Generator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1InputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1Null.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1Object.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1OctetString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1OctetStringParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1OutputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1ParsingException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1Sequence.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1SequenceParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1Set.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1SetParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1StreamParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1TaggedObject.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1TaggedObjectParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\Asn1Tags.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerApplicationSpecific.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerApplicationSpecificParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerOctetString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerOctetStringParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerOutputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerSequence.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerSequenceGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerSequenceParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerSet.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerSetGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerSetParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerTaggedObject.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\BerTaggedObjectParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\ConstructedOctetStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DefiniteLengthInputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerApplicationSpecific.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerBitString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerBmpString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerBoolean.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerEnumerated.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerExternal.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerExternalParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerGeneralString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerGeneralizedTime.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerGraphicString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerIA5String.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerInteger.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerNull.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerNumericString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerObjectIdentifier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerOctetString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerOctetStringParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerOutputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerPrintableString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerSequence.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerSequenceParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerSet.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerSetGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerSetParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerStringBase.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerT61String.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerTaggedObject.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerUniversalString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerUtcTime.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerUtf8String.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerVideotexString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\DerVisibleString.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\IAsn1ApplicationSpecificParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\IAsn1Choice.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\IAsn1Convertible.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\IAsn1String.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\IndefiniteLengthInputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\LazyAsn1InputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\LazyDerSequence.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\LazyDerSet.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\LimitedInputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1\OidTokenizer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Anssi\AnssiNamedCurves.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Anssi\AnssiObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_CryptoPro\CryptoProObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_CryptoPro\ECGost3410NamedCurves.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_CryptoPro\Gost3410NamedParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_CryptoPro\Gost3410ParamSetParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_CryptoPro\Gost3410PublicKeyAlgParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Iana\IanaObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Misc\MiscObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Misc\NetscapeCertType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Misc\NetscapeRevocationUrl.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Misc\VerisignCzagExtension.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Nist\NistNamedCurves.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Nist\NistObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Ocsp\OcspResponse.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Ocsp\OcspResponseStatus.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Ocsp\ResponderID.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Ocsp\ResponseBytes.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Oiw\ElGamalParameter.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Oiw\OiwObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Pkcs\ContentInfo.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Pkcs\DHParameter.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Pkcs\PkcsObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Pkcs\RsassaPssParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Pkcs\SignedData.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Sec\SecNamedCurves.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Sec\SecObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_TeleTrust\TeleTrusTNamedCurves.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_TeleTrust\TeleTrusTObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Utilities\Asn1Dump.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_Utilities\FilterStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\AlgorithmIdentifier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\BasicConstraints.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\CertificateList.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\CrlDistPoint.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\CrlEntry.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\CrlNumber.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\CrlReason.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\DigestInfo.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\DistributionPoint.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\DistributionPointName.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\DsaParameter.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\GeneralName.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\GeneralNames.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\IssuingDistributionPoint.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\KeyUsage.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\ReasonFlags.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\RsaPublicKeyStructure.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\SubjectPublicKeyInfo.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\TbsCertificateList.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\TbsCertificateStructure.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\Time.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\X509CertificateStructure.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\X509DefaultEntryConverter.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\X509Extension.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\X509Extensions.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\X509Name.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\X509NameEntryConverter.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\X509NameTokenizer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X509\X509ObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\DHDomainParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\DHPublicKey.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\DHValidationParms.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\ECNamedCurveTable.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\X962NamedCurves.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\X962Parameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\X9Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\X9ECParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\X9ECParametersHolder.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\X9ECPoint.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\X9FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\X9FieldID.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\X9IntegerConverter.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Asn1_X9\X9ObjectIdentifiers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\AsymmetricCipherKeyPair.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\AsymmetricKeyParameter.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\BufferedAeadBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\BufferedAsymmetricBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\BufferedBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\BufferedCipherBase.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\BufferedIesCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\BufferedStreamCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\Check.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\CipherKeyGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\CryptoException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\DataLengthException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IAsymmetricBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IAsymmetricCipherKeyPairGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IBasicAgreement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IBlockResult.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IBufferedCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\ICipherParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IDerivationFunction.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IDerivationParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IDigest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IDsa.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IMac.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\ISignatureFactory.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\ISigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\ISignerWithRecovery.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IStreamCalculator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IStreamCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IVerifier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IVerifierFactory.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IVerifierFactoryProvider.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IWrapper.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\IXof.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\InvalidCipherTextException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\KeyGenerationParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\MaxBytesExceededException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\OutputLengthException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto\PbeParametersGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Agreement\DHBasicAgreement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Agreement\ECDHBasicAgreement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\GeneralDigest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\Gost3411Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\KeccakDigest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\LongDigest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\MD2Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\MD4Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\MD5Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\NullDigest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\RipeMD128Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\RipeMD160Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\RipeMD256Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\RipeMD320Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\Sha1Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\Sha224Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\Sha256Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\Sha384Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\Sha3Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\Sha512Digest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\Sha512tDigest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\ShakeDigest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\TigerDigest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Digests\WhirlpoolDigest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_EC\CustomNamedCurves.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Encodings\ISO9796d1Encoding.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Encodings\OaepEncoding.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Encodings\Pkcs1Encoding.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\AesEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\AesFastEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\AesWrapEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\BlowfishEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\CamelliaEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\CamelliaWrapEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\Cast5Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\Cast6Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\ChaCha7539Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\ChaChaEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\DesEdeEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\DesEdeWrapEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\DesEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\ElGamalEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\Gost28147Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\HC128Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\HC256Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\IdeaEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\IesEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\NoekeonEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\RC2Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\RC2WrapEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\RC4Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\RC532Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\RC564Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\RC6Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\Rfc3211WrapEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\Rfc3394WrapEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\RijndaelEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\RsaBlindedEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\RsaCoreEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\Salsa20Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\SeedEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\SeedWrapEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\SerpentEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\SerpentEngineBase.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\SkipjackEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\TeaEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\TwofishEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\VmpcEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\VmpcKsa3Engine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Engines\XteaEngine.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Generators\DHBasicKeyPairGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Generators\DHKeyGeneratorHelper.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Generators\DHKeyPairGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Generators\DHParametersHelper.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Generators\DsaKeyPairGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Generators\ECKeyPairGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Generators\ElGamalKeyPairGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Generators\Poly1305KeyGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Generators\RsaKeyPairGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Macs\CMac.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Macs\CbcBlockCipherMac.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Macs\CfbBlockCipherMac.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Macs\Gost28147Mac.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Macs\HMac.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Macs\ISO9797Alg3Mac.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Macs\MacCFBBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Macs\Poly1305.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Macs\SipHash.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Macs\VmpcMac.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\CbcBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\CcmBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\CfbBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\CtsBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\EaxBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\GOfbBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\GcmBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\IAeadBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\OcbBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\OfbBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\OpenPgpCfbBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes\SicBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes_Gcm\GcmUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes_Gcm\IGcmExponentiator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes_Gcm\IGcmMultiplier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes_Gcm\Tables1kGcmExponentiator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Modes_Gcm\Tables8kGcmMultiplier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Operators\Asn1SignatureFactory.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Operators\Asn1VerifierFactory.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Operators\Asn1VerifierFactoryProvider.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Operators\SigCalculator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Operators\SigResult.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Operators\SignerBucket.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Operators\VerifierCalculator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Operators\VerifierResult.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Operators\X509Utilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Paddings\IBlockCipherPadding.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Paddings\ISO10126d2Padding.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Paddings\ISO7816d4Padding.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Paddings\PaddedBufferedBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Paddings\Pkcs7Padding.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Paddings\TbcPadding.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Paddings\X923Padding.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Paddings\ZeroBytePadding.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\AeadParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DHKeyGenerationParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DHKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DHParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DHPrivateKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DHPublicKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DHValidationParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DesEdeParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DesParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DsaKeyGenerationParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DsaKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DsaParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DsaPrivateKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DsaPublicKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\DsaValidationParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ECDomainParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ECKeyGenerationParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ECKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ECPrivateKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ECPublicKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ElGamalKeyGenerationParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ElGamalKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ElGamalParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ElGamalPrivateKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ElGamalPublicKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\Gost3410KeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\Gost3410Parameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\Gost3410PrivateKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\Gost3410PublicKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\Gost3410ValidationParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\IesParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\IesWithCipherParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\Iso18033KdfParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\KdfParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\KeyParameter.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\MqvPrivateParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\MqvPublicParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ParametersWithIV.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ParametersWithRandom.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ParametersWithSBox.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\ParametersWithSalt.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\RC2Parameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\RC5Parameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\RsaBlindingParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\RsaKeyGenerationParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\RsaKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Parameters\RsaPrivateCrtKeyParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Prng\CryptoApiRandomGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Prng\DigestRandomGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Prng\IRandomGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\DsaDigestSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\DsaSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\ECDsaSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\ECGost3410Signer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\ECNRSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\GenericSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\Gost3410DigestSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\Gost3410Signer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\HMacDsaKCalculator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\IDsaKCalculator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\Iso9796d2Signer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\IsoTrailers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\PssSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\RandomDsaKCalculator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\RsaDigestSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Signers\X931Signer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsAgreementCredentials.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsCipherFactory.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsClient.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsContext.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsCredentials.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsEncryptionCredentials.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsKeyExchange.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsPeer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsServer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AbstractTlsSignerCredentials.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AlertDescription.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AlertLevel.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\AlwaysValidVerifyer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ByteQueue.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ByteQueueStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\CertChainType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\Certificate.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\CertificateRequest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\CertificateStatus.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\CertificateStatusRequest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\CertificateStatusType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\Chacha20Poly1305.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ChangeCipherSpec.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\CipherSuite.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\CipherType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ClientCertificateType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\CombinedHash.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\CompressionMethod.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ConnectionEnd.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ContentType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\DatagramTransport.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\DefaultTlsCipherFactory.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\DefaultTlsClient.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\DeferredHash.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\DigestInputBuffer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\DigitallySigned.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ECBasisType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ECCurveType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ECPointFormat.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\EncryptionAlgorithm.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ExporterLabel.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ExtensionType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\FiniteFieldDheGroup.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\HandshakeType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\HashAlgorithm.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\HeartbeatExtension.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\HeartbeatMessageType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\HeartbeatMode.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ICertificateVerifyer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\IClientCredentialsProvider.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\KeyExchangeAlgorithm.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\LegacyTlsAuthentication.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\LegacyTlsClient.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\MacAlgorithm.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\MaxFragmentLength.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\NameType.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\NamedCurve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\NewSessionTicket.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\OcspStatusRequest.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\PrfAlgorithm.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ProtocolVersion.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\RecordStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\SecurityParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ServerDHParams.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ServerName.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\ServerNameList.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\SessionParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\SignatureAlgorithm.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\SignatureAndHashAlgorithm.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\SignerInputBuffer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\Ssl3Mac.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\SupplementalDataEntry.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsAeadCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsAgreementCredentials.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsAuthentication.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsBlockCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsCipherFactory.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsClient.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsClientContext.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsClientContextImpl.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsClientProtocol.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsCompression.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsContext.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsCredentials.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsDHKeyExchange.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsDHUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsDeflateCompression.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsDheKeyExchange.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsDsaSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsDssSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsECDHKeyExchange.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsECDheKeyExchange.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsECDsaSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsEccUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsEncryptionCredentials.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsExtensionsUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsFatalAlert.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsHandshakeHash.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsKeyExchange.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsMac.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsNullCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsNullCompression.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsPeer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsProtocol.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsRsaKeyExchange.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsRsaSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsRsaUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsServer.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsServerContext.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsServerContextImpl.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsSession.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsSessionImpl.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsSigner.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsSignerCredentials.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsStreamCipher.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Tls\TlsUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Crypto_Utilities\Pack.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math\BigInteger.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\AbstractF2mCurve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\AbstractF2mPoint.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\AbstractFpCurve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\AbstractFpPoint.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\ECAlgorithms.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\ECCurve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\ECFieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\ECPoint.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\ECPointBase.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\ECPointMap.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\F2mCurve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\F2mFieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\F2mPoint.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\FpCurve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\FpFieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\FpPoint.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\LongArray.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC\ScaleXPointMap.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Abc\SimpleBigDecimal.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Abc\Tnaf.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Abc\ZTauElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Djb\Curve25519.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Djb\Curve25519Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Djb\Curve25519FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Djb\Curve25519Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP128R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP128R1Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP128R1FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP128R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP160K1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP160K1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP160R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP160R1Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP160R1FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP160R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP160R2Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP160R2Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP160R2FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP160R2Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP192K1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP192K1Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP192K1FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP192K1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP192R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP192R1Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP192R1FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP192R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP224K1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP224K1Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP224K1FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP224K1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP224R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP224R1Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP224R1FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP224R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP256K1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP256K1Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP256K1FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP256K1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP256R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP256R1Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP256R1FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP256R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP384R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP384R1Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP384R1FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP384R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP521R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP521R1Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP521R1FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecP521R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT113Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT113FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT113R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT113R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT113R2Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT113R2Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT131Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT131FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT131R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT131R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT131R2Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT131R2Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT163Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT163FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT163K1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT163K1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT163R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT163R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT163R2Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT163R2Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT193Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT193FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT193R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT193R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT193R2Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT193R2Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT233Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT233FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT233K1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT233K1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT233R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT233R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT239Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT239FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT239K1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT239K1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT283Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT283FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT283K1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT283K1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT283R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT283R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT409Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT409FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT409K1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT409K1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT409R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT409R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT571Field.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT571FieldElement.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT571K1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT571K1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT571R1Curve.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Custom_Sec\SecT571R1Point.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Endo\ECEndomorphism.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Endo\GlvEndomorphism.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Endo\GlvTypeBEndomorphism.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Endo\GlvTypeBParameters.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\AbstractECMultiplier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\ECMultiplier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\FixedPointCombMultiplier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\FixedPointPreCompInfo.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\FixedPointUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\GlvMultiplier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\PreCompInfo.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\WNafL2RMultiplier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\WNafPreCompInfo.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\WNafUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\WTauNafMultiplier.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_EC_Multiplier\WTauNafPreCompInfo.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Field\FiniteFields.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Field\GF2Polynomial.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Field\GenericPolynomialExtensionField.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Field\IExtensionField.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Field\IFiniteField.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Field\IPolynomial.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Field\IPolynomialExtensionField.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Field\PrimeField.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Interleave.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Mod.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat128.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat160.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat192.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat224.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat256.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat320.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat384.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat448.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat512.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Math_Raw\Nat576.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\DigestUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\GeneralSecurityException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\InvalidKeyException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\InvalidParameterException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\KeyException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\MacUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\PublicKeyFactory.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\SecureRandom.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\SecurityUtilityException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\SignatureException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security\SignerUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security_Certificates\CertificateEncodingException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security_Certificates\CertificateException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security_Certificates\CertificateExpiredException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security_Certificates\CertificateNotYetValidException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security_Certificates\CertificateParsingException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Security_Certificates\CrlException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities\Arrays.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities\BigIntegers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities\Enums.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities\IMemoable.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities\Integers.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities\MemoableResetException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities\Platform.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities\Strings.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities\Times.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\CollectionUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\EmptyEnumerable.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\EmptyEnumerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\EnumerableProxy.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\HashSet.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\ISet.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\UnmodifiableDictionary.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\UnmodifiableDictionaryProxy.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\UnmodifiableList.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\UnmodifiableListProxy.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\UnmodifiableSet.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Collections\UnmodifiableSetProxy.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Date\DateTimeObject.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Date\DateTimeUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Encoders\Base64.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Encoders\Base64Encoder.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Encoders\Hex.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Encoders\HexEncoder.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Encoders\IEncoder.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO\BaseInputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO\BaseOutputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO\FilterStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO\PushbackStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO\StreamOverflowException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO\Streams.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO\TeeInputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO\TeeOutputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO_Pem\PemGenerationException.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO_Pem\PemHeader.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO_Pem\PemObject.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO_Pem\PemObjectGenerator.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO_Pem\PemReader.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_IO_Pem\PemWriter.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Net\IPAddress.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\Adler32.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\Deflate.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\InfBlocks.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\InfCodes.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\InfTree.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\Inflate.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\JZlib.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\StaticTree.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\ZOutputStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\ZStream.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_Utilities_Zlib\ZTree.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_X509\IX509Extension.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_X509\PemParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_X509\X509Certificate.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_X509\X509CertificateParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_X509\X509Crl.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_X509\X509CrlEntry.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_X509\X509CrlParser.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_X509\X509ExtensionBase.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_X509\X509SignatureUtilities.cs" />
    <Compile Include="Assets\Scripts\Org_BouncyCastle_X509_Extension\X509ExtensionUtilities.cs" />
    <Compile Include="Assets\Scripts\Package.cs" />
    <Compile Include="Assets\Scripts\PeakAB_VariantProcessors\BuyCoinsProcessor.cs" />
    <Compile Include="Assets\Scripts\PeakAB_VariantProcessors\LevelMoveProcessor.cs" />
    <Compile Include="Assets\Scripts\PeakAB_VariantProcessors\SocialProcessor.cs" />
    <Compile Include="Assets\Scripts\PeakAB_VariantProcessors\StarChestProcessor.cs" />
    <Compile Include="Assets\Scripts\PeakFxColorChange.cs" />
    <Compile Include="Assets\Scripts\PeakFxGrayScale.cs" />
    <Compile Include="Assets\Scripts\PeakGames_Amy_Core_Helpers_Managed\BadWordLanguageNode.cs" />
    <Compile Include="Assets\Scripts\PeakGames_Amy_Core_Helpers_Managed\BadWordListRoot.cs" />
    <Compile Include="Assets\Scripts\PeakGrayScale.cs" />
    <Compile Include="Assets\Scripts\PeakShine.cs" />
    <Compile Include="Assets\Scripts\PinataItem.cs" />
    <Compile Include="Assets\Scripts\Plane3d.cs" />
    <Compile Include="Assets\Scripts\PlatformSupport_Collections_ObjectModel\ObservableDictionary_TKey_TValue_.cs" />
    <Compile Include="Assets\Scripts\PlatformSupport_Collections_Specialized\INotifyCollectionChanged.cs" />
    <Compile Include="Assets\Scripts\PlatformSupport_Collections_Specialized\NotifyCollectionChangedAction.cs" />
    <Compile Include="Assets\Scripts\PlatformSupport_Collections_Specialized\NotifyCollectionChangedEventArgs.cs" />
    <Compile Include="Assets\Scripts\PlatformSupport_Collections_Specialized\NotifyCollectionChangedEventHandler.cs" />
    <Compile Include="Assets\Scripts\PlatformSupport_Collections_Specialized\ReadOnlyList.cs" />
    <Compile Include="Assets\Scripts\PropertyMetadata.cs" />
    <Compile Include="Assets\Scripts\SafeInt.cs" />
    <Compile Include="Assets\Scripts\SelectFriendRow.cs" />
    <Compile Include="Assets\Scripts\SharedImageLibrary.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\BZip2\BZip2.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\BZip2\BZip2Constants.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\BZip2\BZip2Exception.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\BZip2\BZip2InputStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\BZip2\BZip2OutputStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Checksums\Adler32.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Checksums\CRC32.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Checksums\IChecksum.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Checksums\StrangeCRC.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Core\FileSystemScanner.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Core\INameTransform.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Core\IScanFilter.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Core\NameFilter.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Core\PathFilter.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Core\StreamUtils.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Core\WindowsPathUtils.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Encryption\PkzipClassic.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Encryption\ZipAESStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Encryption\ZipAESTransform.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\GZip\GZIPConstants.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\GZip\GZipException.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\GZip\GzipInputStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\GZip\GzipOutputStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Lzw\LzwConstants.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Lzw\LzwException.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Lzw\LzwInputStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Main.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\SharpZipBaseException.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Tar\InvalidHeaderException.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Tar\TarArchive.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Tar\TarBuffer.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Tar\TarEntry.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Tar\TarException.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Tar\TarHeader.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Tar\TarInputStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Tar\TarOutputStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\Deflater.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\DeflaterConstants.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\DeflaterEngine.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\DeflaterHuffman.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\DeflaterPending.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\Inflater.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\InflaterDynHeader.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\InflaterHuffmanTree.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\PendingBuffer.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\Streams\DeflaterOutputStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\Streams\InflaterInputStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\Streams\OutputWindow.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\Compression\Streams\StreamManipulator.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\FastZip.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\IEntryFactory.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\WindowsNameTransform.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\ZipConstants.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\ZipEntry.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\ZipEntryFactory.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\ZipException.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\ZipExtraData.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\ZipFile.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\ZipHelperStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\ZipInputStream.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\ZipNameTransform.cs" />
    <Compile Include="Assets\Scripts\SharpZLib\Zip\ZipOutputStream.cs" />
    <Compile Include="Assets\Scripts\ShortGuid.cs" />
    <Compile Include="Assets\Scripts\ShuffleItem.cs" />
    <Compile Include="Assets\Scripts\SkipMasking.cs" />
    <Compile Include="Assets\Scripts\SoapBubbleAnimation.cs" />
    <Compile Include="Assets\Scripts\SoapItem.cs" />
    <Compile Include="Assets\Scripts\SocialLoading.cs" />
    <Compile Include="Assets\Scripts\Sorting.cs" />
    <Compile Include="Assets\Scripts\SpriteMask.cs" />
    <Compile Include="Assets\Scripts\SpriteMaskingComponent.cs" />
    <Compile Include="Assets\Scripts\SpriteMaskingPart.cs" />
    <Compile Include="Assets\Scripts\StartScene\FacebookButtonTween.cs" />
    <Compile Include="Assets\Scripts\StartScene\LandingButtonPositionFixer.cs" />
    <Compile Include="Assets\Scripts\TMP_BasicXmlTagStack.cs" />
    <Compile Include="Assets\Scripts\TMP_FontWeights.cs" />
    <Compile Include="Assets\Scripts\TMP_LineInfo.cs" />
    <Compile Include="Assets\Scripts\TMP_LinkInfo.cs" />
    <Compile Include="Assets\Scripts\TMP_MeshInfo.cs" />
    <Compile Include="Assets\Scripts\TMP_PageInfo.cs" />
    <Compile Include="Assets\Scripts\TMP_SpriteInfo.cs" />
    <Compile Include="Assets\Scripts\TMP_Vertex.cs" />
    <Compile Include="Assets\Scripts\TMP_WordInfo.cs" />
    <Compile Include="Assets\Scripts\TMP_XmlTagStack.cs" />
    <Compile Include="Assets\Scripts\TMPro_SpriteAssetUtilities\SpriteAssetImportFormats.cs" />
    <Compile Include="Assets\Scripts\TMPro_SpriteAssetUtilities\TexturePacker.cs" />
    <Compile Include="Assets\Scripts\TagAttribute.cs" />
    <Compile Include="Assets\Scripts\TeamNameTextValidator.cs" />
    <Compile Include="Assets\Scripts\TestSceneController.cs" />
    <Compile Include="Assets\Scripts\TextureExtensions.cs" />
    <Compile Include="Assets\Scripts\ToonChestItem.cs" />
    <Compile Include="Assets\Scripts\ToonSocial\Badges.cs" />
    <Compile Include="Assets\Scripts\ToonSocial\SocialCommands.cs" />
    <Compile Include="Assets\Scripts\ToonSocial\SocialHelper.cs" />
    <Compile Include="Assets\Scripts\ToonSocial\SocialSession.cs" />
    <Compile Include="Assets\Scripts\ToonSocial\SocialState.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_actions\CreateTeamAction.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_actions\EditTeamAction.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_actions\FetchTeamAction.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_actions\JoinTeamAction.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_actions\LeaveTeamAction.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_actions\OneAction.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_actions\SearchTeamAction.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\ChatMessage.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\ChatMessageType.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\ComicJson.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\InboxMessage.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\JoinData.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\JoinRequest.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\LifeRequest.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\SocialUser.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\SuggestedTeam.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\Team.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_beans\TeamMember.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_dialogs\BadgeButton.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_dialogs\ChangeTeamDialog.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_dialogs\KickUserDialog.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_dialogs\LeaveTeamDialog.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_dialogs\ReportChatConfirmationDialog.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_dialogs\SocialChangeNameUserDialog.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_dialogs\SocialCreateUserDialog.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_dialogs\SocialSelectBadgeDialog.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_dialogs\StarTournamentAnnouncementDialog.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_dialogs\StarTournamentCreateUserDialog.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_modules\BaseModule.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_modules\SocialChatModule.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_modules\SocialError.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_modules\SocialTeamModule.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_modules\UserModule.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\ChatEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\ChatTab.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\DynamicChatEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\FacebookLeaderboardEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\FriendEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\HelpButtonScript.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\HelpEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\JoinedContainer.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\LifeEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\MyTeamTab.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\PlayersLeaderboardEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\RequestToJoinEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\TeamActionEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\TeamChestEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\TeamEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_joined\TeamLeaderboardEntry.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_notjoined\CreateTeamTab.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_notjoined\NotJoinedContainer.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_notjoined\ReachLevelContainer.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_notjoined\SearchTeamTab.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_notjoined\SelectBadgeRow.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_notjoined\TeamsPage.cs" />
    <Compile Include="Assets\Scripts\ToonSocial_ui_notjoined\TeamsTab.cs" />
    <Compile Include="Assets\Scripts\TranslationQuery.cs" />
    <Compile Include="Assets\Scripts\Triangle.cs" />
    <Compile Include="Assets\Scripts\TutorialAnimationEvents.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll\MultipleHeightPooledVSC_T, U_ where T.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll\OptimisedVerticalScrollController.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll\PooledVerticalScrollController_T, U_ where U.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll\VerticalScrollController.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll\VerticalScrollEntry.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll\VerticalScrollMyEntryHelper.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\FacebookLeaderboardScrollItemData.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\FriendEntryData.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\LivesScrollItemData.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\PlayersLeaderboardScrollItemData.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\StarTournamentEntryData.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\TeamChestEntryData.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\TeamEntryData.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\TeamLeaderboardScrollItemData.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\TeamTournamentPlayerEntryData.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\TeamTournamentTeamEntryData.cs" />
    <Compile Include="Assets\Scripts\Ui_VerticalScroll_Data\VariableHeightEntry.cs" />
    <Compile Include="Assets\Scripts\UrlAndVersion.cs" />
    <Compile Include="Assets\Scripts\Utils\BitmapNumberDisplay.cs" />
    <Compile Include="Assets\Scripts\Utils\BitwiseUtils.cs" />
    <Compile Include="Assets\Scripts\Utils\CaravanDateTime.cs" />
    <Compile Include="Assets\Scripts\Utils\ConsentHelper.cs" />
    <Compile Include="Assets\Scripts\Utils\DialogLibrary.cs" />
    <Compile Include="Assets\Scripts\Utils\EmojiHelper.cs" />
    <Compile Include="Assets\Scripts\Utils\ImageUtils.cs" />
    <Compile Include="Assets\Scripts\Utils\LifeHackData.cs" />
    <Compile Include="Assets\Scripts\Utils\LifeHackHelper.cs" />
    <Compile Include="Assets\Scripts\Utils\Notifications.cs" />
    <Compile Include="Assets\Scripts\Utils\PlayerPrefsKeys.cs" />
    <Compile Include="Assets\Scripts\Utils\RegexLib.cs" />
    <Compile Include="Assets\Scripts\Utils\ScreenResolutionScaler.cs" />
    <Compile Include="Assets\Scripts\Utils\SortingTools.cs" />
    <Compile Include="Assets\Scripts\Utils\StringFormatUtils.cs" />
    <Compile Include="Assets\Scripts\Utils\UserIdChangeListener.cs" />
    <Compile Include="Assets\Scripts\Utils\WaitForThreadedTask.cs" />
    <Compile Include="Assets\Scripts\Utils_Analytics\EventSender.cs" />
    <Compile Include="Assets\Scripts\Utils_BadWordFilter\BadWordController.cs" />
    <Compile Include="Assets\Scripts\Utils_BadWordFilter\BadWordFilter.cs" />
    <Compile Include="Assets\Scripts\Utils_Cloud\CloudService.cs" />
    <Compile Include="Assets\Scripts\Utils_Cloud\CloudUser.cs" />
    <Compile Include="Assets\Scripts\Utils_JapaneseSun\JapaneseSunNew.cs" />
    <Compile Include="Assets\Scripts\Utils_TextEffects\TM_WaveScale.cs" />
    <Compile Include="Assets\Scripts\WallEffect.cs" />
    <Compile Include="Assets\Scripts\WordWrapState.cs" />
    <Compile Include="Assets\Scripts\XML_TagAttribute.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\Data\Singleton.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\Data\XiaomingBaseData.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\Data\XiaomingDataManager.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\Data\XiaomingXMLManager.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\Language\XiaomingLocalizationManager.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\UI\HorizontalScrollController.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\UI\ItemGetDialog.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\UI\NoMoneyDialog.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\UI\PooledHorizontalScrollController.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\UI\SettingPageController.cs" />
    <Compile Include="Assets\Scripts\Xiaoming\UI\SevenDaysBountsDialog.cs" />
    <Compile Include="Assets\Scripts\XiaomingTools\Copy\OldTextMeshProData.cs" />
    <Compile Include="Assets\Scripts\_2dxFX_GrayScale.cs" />
    <Compile Include="Assets\Scripts\_SYL\Animation\AnimationStateContorller.cs" />
    <Compile Include="Assets\Scripts\_SYL\Animation\AnimationStateEvent.cs" />
    <Compile Include="Assets\Scripts\_SYL\Common\ADControl.cs" />
    <Compile Include="Assets\Scripts\_SYL\Common\GameVersionManager.cs" />
    <Compile Include="Assets\Scripts\_SYL\Common\InitGameSet.cs" />
    <Compile Include="Assets\Scripts\_SYL\Common\LocalizationSpriteControl.cs" />
    <Compile Include="Assets\Scripts\_SYL\Common\SDKInit.cs" />
    <Compile Include="Assets\Scripts\_SYL\Common\TestHelper.cs" />
    <Compile Include="Assets\Scripts\_SYL\Common\TipsPanel.cs" />
    <Compile Include="Assets\Scripts\_SYL\Common\USDKDataSupport.cs" />
    <Compile Include="Assets\Scripts\_SYL\DontDestoryOnLoad.cs" />
    <Compile Include="Assets\Scripts\_SYL\ExchangeCDKeyDialog.cs" />
    <Compile Include="Assets\Scripts\_SYL\Manager\RankManager.cs" />
    <Compile Include="Assets\Scripts\_SYL\Manager\RankRequestUrl.cs" />
    <Compile Include="Assets\Scripts\_SYL\Manager\StoreManager.cs" />
    <Compile Include="Assets\Scripts\_SYL\PlayDogSpineAni.cs" />
    <Compile Include="Assets\Scripts\_SYL\Tools_ChangeMat.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\Adjust.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustAndroid.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustAttribution.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustConfig.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustEnvironment.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustEnvironmentExtension.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustEvent.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustEventFailure.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustEventSuccess.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustLogLevel.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustLogLevelExtension.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustSessionFailure.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustSessionSuccess.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\AdjustUtils.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\JSON.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\JSONArray.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\JSONBinaryTag.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\JSONClass.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\JSONData.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\JSONLazyCreator.cs" />
    <Compile Include="Assets\Scripts\com_adjust_sdk\JSONNode.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\AttackSpineboy.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\DataAssetsFromExportsExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\DraggableTransform.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\FootSoldierExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Getting Started Scripts\BasicPlatformerController.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Getting Started Scripts\ConstrainedCamera.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Getting Started Scripts\Raptor.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Getting Started Scripts\SpineBeginnerTwo.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Getting Started Scripts\SpineBlinkPlayer.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Getting Started Scripts\SpineboyBeginnerInput.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Getting Started Scripts\SpineboyBeginnerModel.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Getting Started Scripts\SpineboyBeginnerView.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Getting Started Scripts\SpineboyTargetController.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Getting Started Scripts\TransitionDictionaryExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Goblins.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\HandleEventWithAudioExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\HurtFlashEffect.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\MaterialPropertyBlockExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Mix and Match Character Customize\EquipAssetExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Mix and Match Character Customize\EquipButtonExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Mix and Match Character Customize\EquipSystemExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Mix and Match Character Customize\EquipsVisualsComponentExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\MixAndMatch.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\MixAndMatchGraphic.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\RaggedySpineboy.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\ReloadSceneOnKeyDown.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Rotator.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\BoneLocalOverride.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\CombinedSkin.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\Legacy\AtlasRegionAttacher.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\Legacy\CustomSkin.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\Legacy\SpriteAttacher.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\Sample VertexEffects\JitterEffectExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\Sample VertexEffects\TwoByTwoTransformEffectExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\SkeletonAnimationMulti\SkeletonAnimationMulti.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\SkeletonColorInitialize.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\SlotTintBlackFollower.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Sample Components\SpineEventUnityHandler.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\SpawnFromSkeletonDataExample.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\SpineGauge.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\Spineboy.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\SpineboyBodyTilt.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\SpineboyFacialExpression.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\SpineboyFootplanter.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\SpineboyFreeze.cs" />
    <Compile Include="Assets\Spine Examples\Scripts\SpineboyPole.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Animation.cs" />
    <Compile Include="Assets\Spine\spine-csharp\AnimationState.cs" />
    <Compile Include="Assets\Spine\spine-csharp\AnimationStateData.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Atlas.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\AtlasAttachmentLoader.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\Attachment.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\AttachmentLoader.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\AttachmentType.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\BoundingBoxAttachment.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\ClippingAttachment.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\MeshAttachment.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\PathAttachment.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\PointAttachment.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\RegionAttachment.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Attachments\VertexAttachment.cs" />
    <Compile Include="Assets\Spine\spine-csharp\BlendMode.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Bone.cs" />
    <Compile Include="Assets\Spine\spine-csharp\BoneData.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Event.cs" />
    <Compile Include="Assets\Spine\spine-csharp\EventData.cs" />
    <Compile Include="Assets\Spine\spine-csharp\ExposedList.cs" />
    <Compile Include="Assets\Spine\spine-csharp\IConstraint.cs" />
    <Compile Include="Assets\Spine\spine-csharp\IUpdatable.cs" />
    <Compile Include="Assets\Spine\spine-csharp\IkConstraint.cs" />
    <Compile Include="Assets\Spine\spine-csharp\IkConstraintData.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Json.cs" />
    <Compile Include="Assets\Spine\spine-csharp\MathUtils.cs" />
    <Compile Include="Assets\Spine\spine-csharp\PathConstraint.cs" />
    <Compile Include="Assets\Spine\spine-csharp\PathConstraintData.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Skeleton.cs" />
    <Compile Include="Assets\Spine\spine-csharp\SkeletonBinary.cs" />
    <Compile Include="Assets\Spine\spine-csharp\SkeletonBounds.cs" />
    <Compile Include="Assets\Spine\spine-csharp\SkeletonClipping.cs" />
    <Compile Include="Assets\Spine\spine-csharp\SkeletonData.cs" />
    <Compile Include="Assets\Spine\spine-csharp\SkeletonJson.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Skin.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Slot.cs" />
    <Compile Include="Assets\Spine\spine-csharp\SlotData.cs" />
    <Compile Include="Assets\Spine\spine-csharp\TransformConstraint.cs" />
    <Compile Include="Assets\Spine\spine-csharp\TransformConstraintData.cs" />
    <Compile Include="Assets\Spine\spine-csharp\Triangulator.cs" />
    <Compile Include="Assets\Spine\spine-unity\Asset Types\AnimationReferenceAsset.cs" />
    <Compile Include="Assets\Spine\spine-unity\Asset Types\AtlasAsset.cs" />
    <Compile Include="Assets\Spine\spine-unity\Asset Types\EventDataReferenceAsset.cs" />
    <Compile Include="Assets\Spine\spine-unity\Asset Types\RegionlessAttachmentLoader.cs" />
    <Compile Include="Assets\Spine\spine-unity\Asset Types\SkeletonDataAsset.cs" />
    <Compile Include="Assets\Spine\spine-unity\Components\BoneFollower.cs" />
    <Compile Include="Assets\Spine\spine-unity\Components\PointFollower.cs" />
    <Compile Include="Assets\Spine\spine-unity\Components\SkeletonAnimation.cs" />
    <Compile Include="Assets\Spine\spine-unity\Components\SkeletonAnimator.cs" />
    <Compile Include="Assets\Spine\spine-unity\Components\SkeletonRenderer.cs" />
    <Compile Include="Assets\Spine\spine-unity\ISkeletonAnimation.cs" />
    <Compile Include="Assets\Spine\spine-unity\Mesh Generation\DoubleBuffered.cs" />
    <Compile Include="Assets\Spine\spine-unity\Mesh Generation\SpineMesh.cs" />
    <Compile Include="Assets\Spine\spine-unity\Mesh Generation\Unused\ArraysMeshGenerator.cs" />
    <Compile Include="Assets\Spine\spine-unity\Mesh Generation\Unused\ArraysSimpleMeshGenerator.cs" />
    <Compile Include="Assets\Spine\spine-unity\Mesh Generation\Unused\ArraysSubmeshSetMeshGenerator.cs" />
    <Compile Include="Assets\Spine\spine-unity\Mesh Generation\Unused\ArraysSubmeshedMeshGenerator.cs" />
    <Compile Include="Assets\Spine\spine-unity\Mesh Generation\Unused\DoubleBufferedMesh.cs" />
    <Compile Include="Assets\Spine\spine-unity\Mesh Generation\Unused\ISimpleMeshGenerator.cs" />
    <Compile Include="Assets\Spine\spine-unity\Mesh Generation\Unused\ISubmeshedMeshGenerator.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\AttachmentTools\AttachmentTools.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\BoundingBoxFollower\BoundingBoxFollower.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\CustomMaterials\SkeletonRendererCustomMaterials.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Ghost\SkeletonGhost.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Ghost\SkeletonGhostRenderer.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Ragdoll\SkeletonRagdoll.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Ragdoll\SkeletonRagdoll2D.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\SkeletonGraphic\BoneFollowerGraphic.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\SkeletonGraphic\SkeletonGraphic.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\SkeletonGraphic\SkeletonGraphicMirror.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\SkeletonRenderSeparator\SkeletonPartsRenderer.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\SkeletonRenderSeparator\SkeletonRenderSeparator.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\SkeletonUtility Modules\SkeletonUtilityEyeConstraint.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\SkeletonUtility Modules\SkeletonUtilityGroundConstraint.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\SkeletonUtility Modules\SkeletonUtilityKinematicShadow.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\SlotBlendModes\SlotBlendModes.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\TK2D\SpriteCollectionAttachmentLoader.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Timeline\PlayableHandle Component\SkeletonAnimationPlayableHandle.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Timeline\PlayableHandle Component\SpinePlayableHandleBase.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateBehaviour.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateClip.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateMixerBehaviour.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Timeline\SpineAnimationState\SpineAnimationStateTrack.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipBehaviour.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipClip.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipMixerBehaviour.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\Timeline\SpineSkeletonFlip\SpineSkeletonFlipTrack.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\YieldInstructions\WaitForSpineAnimationComplete.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\YieldInstructions\WaitForSpineEvent.cs" />
    <Compile Include="Assets\Spine\spine-unity\Modules\YieldInstructions\WaitForSpineTrackEntryEnd.cs" />
    <Compile Include="Assets\Spine\spine-unity\SkeletonExtensions.cs" />
    <Compile Include="Assets\Spine\spine-unity\SkeletonUtility\SkeletonUtility.cs" />
    <Compile Include="Assets\Spine\spine-unity\SkeletonUtility\SkeletonUtilityBone.cs" />
    <Compile Include="Assets\Spine\spine-unity\SkeletonUtility\SkeletonUtilityConstraint.cs" />
    <Compile Include="Assets\Spine\spine-unity\SpineAttributes.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\Dll\IOSSDK.dll" />
    <None Include="Assets\Spine Examples\Spine\Dragon\dragon.atlas.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro.cginc" />
    <None Include="Assets\Dll\UserAgent.dll" />
    <None Include="Assets\Spine\spine-unity\version.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\SkeletonRenderSeparator\SkeletonRenderSeparator.txt" />
    <None Include="Assets\Resources\spineanimations\celebration\skeleton.atlas.txt" />
    <None Include="Assets\Shaders\Spine\Utility\HiddenPass.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CGIncludes\ShaderShared.cginc" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CGIncludes\ShaderMaths.cginc" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteSpecular.cginc" />
    <None Include="Assets\OwnTools\UTools\Config\IOSPackageConfig.xml" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap.shader" />
    <None Include="Assets\Resources\spineanimations\StartAni\skeleton.atlas.txt" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Spine-Skeleton-Tint.shader" />
    <None Include="Assets\Shaders\SpriteDefault.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\SpriteVertexLighting.cginc" />
    <None Include="Assets\LanguageAssetF.txt" />
    <None Include="Assets\Spine Examples\Spine\Eyes\eyes.atlas.txt" />
    <None Include="Assets\Resources\spineanimations\ToonChestBox\skeleton.atlas.txt" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CameraNormalsTexture.shader" />
    <None Include="Assets\OwnTools\KV\AttributeTool\AttributeTool.dll" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\SpritesUnlit.shader" />
    <None Include="Assets\Resources\spineanimations\StarChestBox\skeleton.atlas.txt" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\SpriteLighting.cginc" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Spine-Skeleton-Fill.shader" />
    <None Include="Assets\LevelMetaData\0001.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets\Dll\LAnalytics.dll" />
    <None Include="Assets\Shaders\ClearStencil.shader" />
    <None Include="Assets\Shaders\Spine\Utility\Hidden-Spine-Bones.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\SpriteUnlit.cginc" />
    <None Include="Assets\Spine\spine-unity\Modules\SkeletonGraphic\Shaders\Spine-SkeletonGraphic-TintBlack.shader" />
    <None Include="Assets\Dll\UTools.dll" />
    <None Include="Assets\TextMesh Pro\Lang\ZH.txt" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteUnlit.cginc" />
    <None Include="Assets\Spine Examples\Spine\Goblins\goblins.atlas.txt" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CameraDepthNormalsTexture.shader" />
    <None Include="Assets\Shaders\SpriteFilledfinish.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteShadows.cginc" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteVertexLighting.cginc" />
    <None Include="Assets\Spine Examples\Spine\Hero\hero-pro.atlas.txt" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CameraDepthTexture.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Surface.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\Ghost\Shaders\Spine-Special-Skeleton-Ghost.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\SpritePixelLighting.cginc" />
    <None Include="Assets\TextMesh Pro\Sprites\EmojiOne Attribution.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets\Dll\UnityEditor.UiOS.Extensions.Xcode.dll" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF.shader" />
    <None Include="Assets\TextMesh Pro\Lang\ZH_TW.txt" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro_Properties.cginc" />
    <None Include="Assets\Spine Examples\Spine\Strechyman\stretchyman-diffuse-pma.atlas.txt" />
    <None Include="Assets\Dll\GMGSDK.dll" />
    <None Include="Assets\Spine Examples\Spine\Spineunitygirl\Doi.atlas.txt" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Straight Alpha\Spine-Straight-Skeleton-Tint.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMPro_Surface.cginc" />
    <None Include="Assets\Spine Examples\Spine\Gauge\Gauge.atlas.txt" />
    <None Include="Assets\Spine Examples\Spine\Dragon\license.txt" />
    <None Include="Assets\Resources\spineanimations\bear_ingame\skelet1.atlas.txt" />
    <None Include="Assets\Dll\Howin.dll" />
    <None Include="Assets\Spine\spine-unity\Modules\SkeletonGraphic\Shaders\Spine-SkeletonGraphic.shader" />
    <None Include="Assets\TextMesh Pro\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\Shaders\SpriteDiffuse.shader" />
    <None Include="Assets\Dll\LitJson.dll" />
    <None Include="Assets\Shaders\SpriteMask.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\SlotBlendModes\Spine-Skeleton-PMA-Multiply.shader" />
    <None Include="Assets\Spine Examples\Spine\spineboy-pro\spineboy-pro.atlas.txt" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\SpriteDepthNormalsTexture.shader" />
    <None Include="Assets\Resources\LineBreaking Following Characters.txt" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpriteLighting.cginc" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\SpritesVertexLit.shader" />
    <None Include="Assets\Resources\LineBreaking Leading Characters.txt" />
    <None Include="Assets\Spine Examples\Spine\FootSoldier\FS_White.atlas.txt" />
    <None Include="Assets\Spine Examples\Spine\spineboy-unity\spineboy.atlas.txt" />
    <None Include="Assets\OwnTools\UTools\Resources\Language\en.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Straight Alpha\Spine-Straight-Skeleton-Fill.shader" />
    <None Include="Assets\OwnTools\UTools\Resources\Language\zh_Hans.txt" />
    <None Include="Assets\Spine Examples\Spine\Hero\license.txt" />
    <None Include="Assets\Dll\URankModule.dll" />
    <None Include="Assets\Spine\spine-unity\Modules\CustomMaterials\SkeletonRendererCustomMaterials.txt" />
    <None Include="Assets\Dll\UI.dll" />
    <None Include="Assets\Dll\LGPOfflineSDK.dll" />
    <None Include="Assets\Dll\LAdModule.dll" />
    <None Include="Assets\Dll\LSDKInterface.dll" />
    <None Include="Assets\Shaders\Spine\Spine-SkeletonLit.shader" />
    <None Include="Assets\Spine Examples\Spine\Raggedy Spineboy\Raggedy Spineboy.atlas.txt" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF Overlay.shader" />
    <None Include="Assets\Spine Examples\Spine\Raptor\raptor.atlas.txt" />
    <None Include="Assets\Shaders\Spine\Spine-Skeleton.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Bitmap-Mobile.shader" />
    <None Include="Assets\OwnTools\UTools\Config\packageConfig.xml" />
    <None Include="Assets\Spine\spine-unity\Modules\SlotBlendModes\Spine-Skeleton-PMA-Screen.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\ShaderShared.cginc" />
    <None Include="Assets\Shaders\Spine\Spine-Skeleton-TintBlack.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_SDF-Mobile.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\SpriteShadows.cginc" />
    <None Include="Assets\LanguageAsset.txt" />
    <None Include="Assets\Dll\UCommonModule.dll" />
    <None Include="Assets\Spine Examples\Spine\FootSoldier\Equipment\Equipment.atlas.txt" />
    <None Include="Assets\Dll\LauguageKeys.dll" />
    <None Include="Assets\Spine Examples\Spine\FootSoldier\license.txt" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\SpritesPixelLit.shader" />
    <None Include="Assets\Spine\spine-unity\Modules\Shaders\Sprite\CGIncludes\SpritePixelLighting.cginc" />
    <None Include="Assets\Materials\Progress.shader" />
    <None Include="Assets\TextMesh Pro\Resources\Shaders\TMP_Sprite.shader" />
    <None Include="Assets\Resources\language\Language.xml" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UNETModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UNETModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\Managed\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="GMGSDK">
      <HintPath>Assets\Dll\GMGSDK.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Howin">
      <HintPath>Assets\Dll\Howin.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="IOSSDK">
      <HintPath>Assets\Dll\IOSSDK.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LAdModule">
      <HintPath>Assets\Dll\LAdModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LAnalytics">
      <HintPath>Assets\Dll\LAnalytics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LauguageKeys">
      <HintPath>Assets\Dll\LauguageKeys.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LGPOfflineSDK">
      <HintPath>Assets\Dll\LGPOfflineSDK.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LitJson">
      <HintPath>Assets\Dll\LitJson.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LSDKInterface">
      <HintPath>Assets\Dll\LSDKInterface.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UCommonModule">
      <HintPath>Assets\Dll\UCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UI">
      <HintPath>Assets\Dll\UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UiOS.Extensions.Xcode">
      <HintPath>Assets\Dll\UnityEditor.UiOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="URankModule">
      <HintPath>Assets\Dll\URankModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UserAgent">
      <HintPath>Assets\Dll\UserAgent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UTools">
      <HintPath>Assets\Dll\UTools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AttributeTool">
      <HintPath>Assets\OwnTools\KV\AttributeTool\AttributeTool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Analytics">
      <HintPath>Assets\Plugins\Analytics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="AnalyticsUnityLibrariesAndroid">
      <HintPath>Assets\Plugins\AnalyticsUnityLibrariesAndroid.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ArabicSupport">
      <HintPath>Assets\Plugins\ArabicSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Backend">
      <HintPath>Assets\Plugins\Backend.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="BackendSerializer">
      <HintPath>Assets\Plugins\BackendSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="BillingUnityLibrariesAndroid">
      <HintPath>Assets\Plugins\BillingUnityLibrariesAndroid.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween43">
      <HintPath>Assets\Plugins\DOTween43.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween46">
      <HintPath>Assets\Plugins\DOTween46.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween50">
      <HintPath>Assets\Plugins\DOTween50.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Facebook.Unity.Settings">
      <HintPath>Assets\Plugins\Facebook.Unity.Settings.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LevelDataMeta">
      <HintPath>Assets\Plugins\LevelDataMeta.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LevelDataMetaSerializer">
      <HintPath>Assets\Plugins\LevelDataMetaSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="protobuf-net">
      <HintPath>Assets\Plugins\protobuf-net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Validator">
      <HintPath>Assets\Plugins\Validator.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\ref\2.0.0\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netstandard\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\Extensions\2.0.0\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>E:\ide\Unity\Unity 2019.4.40f1\Editor\Data\NetStandard\compat\2.0.0\shims\netfx\System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="com.unity.multiplayer-hlapi.Editor">
      <HintPath>Library\ScriptAssemblies\com.unity.multiplayer-hlapi.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VSCode.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library\ScriptAssemblies\Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="com.unity.multiplayer-weaver.Editor">
      <HintPath>Library\ScriptAssemblies\com.unity.multiplayer-weaver.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XR.LegacyInputHelpers">
      <HintPath>Library\ScriptAssemblies\UnityEngine.XR.LegacyInputHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.Rider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Sprite.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Tilemap.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.2D.Tilemap.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpatialTracking">
      <HintPath>Library\ScriptAssemblies\UnityEditor.SpatialTracking.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpatialTracking">
      <HintPath>Library\ScriptAssemblies\UnityEngine.SpatialTracking.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XR.LegacyInputHelpers">
      <HintPath>Library\ScriptAssemblies\UnityEditor.XR.LegacyInputHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>Library\ScriptAssemblies\Unity.PlasticSCM.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="com.unity.multiplayer-hlapi.Runtime">
      <HintPath>Library\ScriptAssemblies\com.unity.multiplayer-hlapi.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="GenerateTargetFrameworkMonikerAttribute" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>
